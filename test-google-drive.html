<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Google Drive - النسور الماسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #ea4335 100%);
            color: white;
            padding: 20px;
            text-align: center;
            min-height: 100vh;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: rgba(52, 168, 83, 0.8); }
        .error { background: rgba(234, 67, 53, 0.8); }
        .warning { background: rgba(251, 188, 5, 0.8); }
        .info { background: rgba(66, 133, 244, 0.8); }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid white;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        #log {
            background: rgba(0, 0, 0, 0.4);
            padding: 20px;
            border-radius: 10px;
            text-align: left;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .user-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            display: none;
        }
        .user-info.show {
            display: block;
        }
        .google-logo {
            font-size: 24px;
            margin-right: 8px;
        }
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            text-align: right;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #fff;
        }
        .step p {
            margin: 5px 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 اختبار Google Drive - النسور الماسية</h1>
        
        <div class="step">
            <h3>📋 خطوات الإعداد المطلوبة:</h3>
            <p>1. إنشاء مشروع في Google Cloud Console</p>
            <p>2. تفعيل Google Drive API</p>
            <p>3. إنشاء OAuth 2.0 Client ID</p>
            <p>4. إنشاء API Key</p>
            <p>5. تحديث التكوين في google-drive-sync.js</p>
        </div>
        
        <div id="status" class="status info">
            جاري التحقق من إعداد Google Drive...
        </div>
        
        <div id="userInfo" class="user-info">
            <h3>👤 معلومات المستخدم:</h3>
            <p id="userName"></p>
            <p id="userEmail"></p>
        </div>
        
        <div>
            <button onclick="checkConfig()">🔍 فحص التكوين</button>
            <button onclick="signInToGoogle()" id="signInBtn">
                <span class="google-logo">🔐</span>تسجيل دخول Google
            </button>
            <button onclick="testDriveAccess()" id="testDriveBtn" disabled>📁 اختبار Google Drive</button>
            <button onclick="createTestFile()" id="createFileBtn" disabled>📄 إنشاء ملف تجريبي</button>
            <button onclick="listFiles()" id="listFilesBtn" disabled>📋 عرض الملفات</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>
        
        <div id="log"></div>
    </div>

    <!-- Google API -->
    <script src="https://apis.google.com/js/api.js"></script>
    
    <script>
        // Google Drive Configuration - يجب تحديثها
        const GOOGLE_CONFIG = {
            CLIENT_ID: 'PASTE_YOUR_CLIENT_ID_HERE.apps.googleusercontent.com',
            API_KEY: 'PASTE_YOUR_API_KEY_HERE',
            DISCOVERY_DOC: 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest',
            SCOPES: 'https://www.googleapis.com/auth/drive.file'
        };

        let gapi = null;
        let isSignedIn = false;
        let currentUser = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                error: '#ea4335',
                success: '#34a853', 
                warning: '#fbbc05',
                info: '#4285f4'
            };
            
            logDiv.innerHTML += `<div style="color: ${colors[type]}; margin: 5px 0; padding: 5px; border-left: 3px solid ${colors[type]}; padding-left: 10px;">
                <strong>[${time}]</strong> ${message}
            </div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function updateButtons() {
            document.getElementById('signInBtn').disabled = !gapi || isSignedIn;
            document.getElementById('testDriveBtn').disabled = !isSignedIn;
            document.getElementById('createFileBtn').disabled = !isSignedIn;
            document.getElementById('listFilesBtn').disabled = !isSignedIn;
        }

        function showUserInfo(user) {
            const userInfo = document.getElementById('userInfo');
            const profile = user.getBasicProfile();
            
            document.getElementById('userName').textContent = `الاسم: ${profile.getName()}`;
            document.getElementById('userEmail').textContent = `البريد: ${profile.getEmail()}`;
            
            userInfo.classList.add('show');
        }

        async function checkConfig() {
            log('🔍 فحص التكوين...', 'info');
            
            if (GOOGLE_CONFIG.CLIENT_ID === 'YOUR_CLIENT_ID_HERE.apps.googleusercontent.com') {
                log('❌ لم يتم تحديث CLIENT_ID', 'error');
                updateStatus('يجب تحديث التكوين أولاً', 'error');
                return false;
            }
            
            if (GOOGLE_CONFIG.API_KEY === 'YOUR_API_KEY_HERE') {
                log('❌ لم يتم تحديث API_KEY', 'error');
                updateStatus('يجب تحديث التكوين أولاً', 'error');
                return false;
            }
            
            log('✅ التكوين يبدو صحيحاً', 'success');
            
            // Try to load Google API
            try {
                await initializeGoogleAPI();
                log('✅ تم تحميل Google API بنجاح', 'success');
                updateStatus('Google API جاهز', 'success');
                return true;
            } catch (error) {
                log('❌ فشل في تحميل Google API: ' + error.message, 'error');
                updateStatus('خطأ في تحميل Google API', 'error');
                return false;
            }
        }

        async function initializeGoogleAPI() {
            return new Promise((resolve, reject) => {
                gapi = window.gapi;
                
                gapi.load('auth2', () => {
                    gapi.auth2.init({
                        client_id: GOOGLE_CONFIG.CLIENT_ID
                    }).then(() => {
                        gapi.load('client', () => {
                            gapi.client.init({
                                apiKey: GOOGLE_CONFIG.API_KEY,
                                clientId: GOOGLE_CONFIG.CLIENT_ID,
                                discoveryDocs: [GOOGLE_CONFIG.DISCOVERY_DOC],
                                scope: GOOGLE_CONFIG.SCOPES
                            }).then(() => {
                                updateButtons();
                                resolve();
                            }).catch(reject);
                        });
                    }).catch(reject);
                });
            });
        }

        async function signInToGoogle() {
            try {
                log('🔐 محاولة تسجيل الدخول...', 'info');
                
                const authInstance = gapi.auth2.getAuthInstance();
                currentUser = await authInstance.signIn();
                
                isSignedIn = true;
                updateButtons();
                showUserInfo(currentUser);
                
                log('✅ تم تسجيل الدخول بنجاح', 'success');
                log(`👤 مرحباً ${currentUser.getBasicProfile().getName()}`, 'success');
                updateStatus('تم تسجيل الدخول بنجاح', 'success');
                
            } catch (error) {
                log('❌ فشل في تسجيل الدخول: ' + error.error, 'error');
                updateStatus('فشل في تسجيل الدخول', 'error');
            }
        }

        async function testDriveAccess() {
            try {
                log('📁 اختبار الوصول إلى Google Drive...', 'info');
                
                const response = await gapi.client.drive.about.get({
                    fields: 'user, storageQuota'
                });
                
                const about = response.result;
                log('✅ تم الوصول إلى Google Drive بنجاح', 'success');
                log(`📊 المساحة المستخدمة: ${Math.round(about.storageQuota.usage / 1024 / 1024)} MB`, 'info');
                log(`📊 المساحة الإجمالية: ${Math.round(about.storageQuota.limit / 1024 / 1024 / 1024)} GB`, 'info');
                
                updateStatus('Google Drive يعمل بشكل مثالي', 'success');
                
            } catch (error) {
                log('❌ فشل في الوصول إلى Google Drive: ' + error.result.error.message, 'error');
                updateStatus('خطأ في الوصول إلى Google Drive', 'error');
            }
        }

        async function createTestFile() {
            try {
                log('📄 إنشاء ملف تجريبي...', 'info');
                
                const testData = {
                    message: 'اختبار من تطبيق النسور الماسية',
                    timestamp: new Date().toISOString(),
                    user: currentUser.getBasicProfile().getName()
                };
                
                const fileContent = JSON.stringify(testData, null, 2);
                const fileName = `test_${Date.now()}.json`;
                
                // Create file metadata
                const fileMetadata = {
                    name: fileName
                };
                
                // Create form data
                const form = new FormData();
                form.append('metadata', new Blob([JSON.stringify(fileMetadata)], {type: 'application/json'}));
                form.append('file', new Blob([fileContent], {type: 'application/json'}));
                
                // Upload file
                const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
                    method: 'POST',
                    headers: new Headers({
                        'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
                    }),
                    body: form
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log('✅ تم إنشاء الملف بنجاح', 'success');
                    log(`📄 اسم الملف: ${result.name}`, 'info');
                    log(`🆔 معرف الملف: ${result.id}`, 'info');
                    updateStatus('تم إنشاء الملف بنجاح', 'success');
                } else {
                    throw new Error('فشل في رفع الملف');
                }
                
            } catch (error) {
                log('❌ فشل في إنشاء الملف: ' + error.message, 'error');
                updateStatus('فشل في إنشاء الملف', 'error');
            }
        }

        async function listFiles() {
            try {
                log('📋 جاري عرض الملفات...', 'info');
                
                const response = await gapi.client.drive.files.list({
                    pageSize: 10,
                    fields: 'files(id, name, createdTime, size)'
                });
                
                const files = response.result.files;
                
                if (files && files.length > 0) {
                    log(`📁 تم العثور على ${files.length} ملف:`, 'success');
                    files.forEach((file, index) => {
                        const size = file.size ? `${Math.round(file.size / 1024)} KB` : 'غير محدد';
                        const date = new Date(file.createdTime).toLocaleDateString('ar-SA');
                        log(`${index + 1}. ${file.name} (${size}) - ${date}`, 'info');
                    });
                } else {
                    log('📭 لا توجد ملفات', 'warning');
                }
                
                updateStatus(`تم عرض ${files.length} ملف`, 'success');
                
            } catch (error) {
                log('❌ فشل في عرض الملفات: ' + error.result.error.message, 'error');
                updateStatus('فشل في عرض الملفات', 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('🗑️ تم مسح السجل', 'info');
        }

        // Initialize on load
        window.onload = function() {
            log('🌐 مرحباً بك في اختبار Google Drive', 'info');
            log('📋 اضغط "فحص التكوين" للبدء', 'info');
            updateButtons();
        };
    </script>
</body>
</html>
