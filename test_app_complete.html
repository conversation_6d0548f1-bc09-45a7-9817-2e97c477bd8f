<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - تطبيق واصل TV</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2196F3;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #2196F3;
        }
        
        .test-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-btn:hover {
            background: #45a049;
        }
        
        .test-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-weight: bold;
        }
        
        .result-success {
            background: #d4edda;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .result-loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .summary {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .run-all-btn {
            background: #FF5722;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
        }
        
        .run-all-btn:hover {
            background: #E64A19;
        }
        
        .info-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .checklist li:before {
            content: "☐ ";
            color: #666;
            margin-left: 10px;
        }
        
        .checklist li.checked:before {
            content: "✅ ";
            color: #4CAF50;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار شامل - تطبيق واصل TV</h1>
        
        <div class="info-box">
            <h3>📋 دليل الاختبار الشامل</h3>
            <p>هذه الأداة تختبر جميع جوانب تطبيق واصل TV للتأكد من عمله بشكل صحيح</p>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>
        <p id="progress-text">جاهز للبدء - 0% مكتمل</p>
        
        <button class="run-all-btn" onclick="runAllTests()">🚀 تشغيل جميع الاختبارات</button>
        
        <!-- اختبار الاتصال بالسيرفر -->
        <div class="test-section">
            <div class="test-header">
                <div class="test-title">🌐 اختبار الاتصال بسيرفر واصل TV</div>
                <button class="test-btn" onclick="testServerConnection()">اختبار</button>
            </div>
            <p>التحقق من الاتصال بسيرفر maventv.one:80</p>
            <div id="server-result"></div>
        </div>
        
        <!-- اختبار API -->
        <div class="test-section">
            <div class="test-header">
                <div class="test-title">📡 اختبار Xtream Codes API</div>
                <button class="test-btn" onclick="testAPI()">اختبار</button>
            </div>
            <p>التحقق من استجابة API ومعلومات المستخدم</p>
            <div id="api-result"></div>
        </div>
        
        <!-- اختبار قائمة القنوات -->
        <div class="test-section">
            <div class="test-header">
                <div class="test-title">📺 اختبار قائمة القنوات</div>
                <button class="test-btn" onclick="testChannelsList()">اختبار</button>
            </div>
            <p>التحقق من تحميل قائمة القنوات والأقسام</p>
            <div id="channels-result"></div>
        </div>
        
        <!-- اختبار تنسيقات Stream -->
        <div class="test-section">
            <div class="test-header">
                <div class="test-title">🎬 اختبار تنسيقات Stream</div>
                <button class="test-btn" onclick="testStreamFormats()">اختبار</button>
            </div>
            <p>اختبار تنسيقات مختلفة للـ streams (.ts, .m3u8, .mp4)</p>
            <div id="stream-result"></div>
        </div>
        
        <!-- اختبار القنوات التجريبية -->
        <div class="test-section">
            <div class="test-header">
                <div class="test-title">🧪 اختبار القنوات التجريبية</div>
                <button class="test-btn" onclick="testSampleChannels()">اختبار</button>
            </div>
            <p>اختبار القنوات التجريبية المضمونة التشغيل</p>
            <div id="sample-result"></div>
        </div>
        
        <!-- قائمة فحص يدوي -->
        <div class="test-section">
            <div class="test-header">
                <div class="test-title">📋 قائمة فحص يدوي للتطبيق</div>
                <button class="test-btn" onclick="toggleManualChecklist()">عرض/إخفاء</button>
            </div>
            <div id="manual-checklist" style="display: none;">
                <p>اختبر هذه النقاط يدوياً على التطبيق:</p>
                <ul class="checklist">
                    <li onclick="toggleCheck(this)">التطبيق يفتح بدون أخطاء</li>
                    <li onclick="toggleCheck(this)">الأيقونة تظهر بشكل صحيح في قائمة التطبيقات</li>
                    <li onclick="toggleCheck(this)">الأقسام الأربع تظهر: أخبار، رياضة، أطفال، ترفيه</li>
                    <li onclick="toggleCheck(this)">عند الضغط على قسم تظهر قنواته فقط</li>
                    <li onclick="toggleCheck(this)">عند الضغط على قناة يفتح المشغل</li>
                    <li onclick="toggleCheck(this)">رسالة "اختبار الرابط..." تظهر أولاً</li>
                    <li onclick="toggleCheck(this)">القنوات التجريبية تعمل بنجاح</li>
                    <li onclick="toggleCheck(this)">أدوات التشخيص (🔍) تعمل</li>
                    <li onclick="toggleCheck(this)">معلومات السيرفر صحيحة في التشخيص</li>
                    <li onclick="toggleCheck(this)">رسائل الخطأ واضحة ومفيدة</li>
                </ul>
            </div>
        </div>
        
        <div class="summary" id="summary" style="display: none;">
            <h3>📊 ملخص نتائج الاختبار</h3>
            <div id="summary-content"></div>
        </div>
    </div>

    <script>
        const SERVER_URL = 'http://maventv.one:80';
        const USERNAME = 'odaitv';
        const PASSWORD = 'Odai2030';
        
        let testResults = {};
        let currentTest = 0;
        const totalTests = 5;
        
        function updateProgress() {
            const progress = (currentTest / totalTests) * 100;
            document.getElementById('progress').style.width = progress + '%';
            document.getElementById('progress-text').textContent = 
                `${currentTest}/${totalTests} اختبارات مكتملة - ${Math.round(progress)}%`;
        }
        
        async function testServerConnection() {
            const resultDiv = document.getElementById('server-result');
            resultDiv.innerHTML = '<div class="test-result result-loading">🔄 اختبار الاتصال بالسيرفر...</div>';
            
            try {
                const response = await fetch(`${SERVER_URL}/player_api.php?username=${USERNAME}&password=${PASSWORD}`, {
                    method: 'GET',
                    mode: 'no-cors'
                });
                
                testResults.server = { success: true, message: 'الاتصال ناجح' };
                resultDiv.innerHTML = '<div class="test-result result-success">✅ الاتصال بالسيرفر ناجح</div>';
                
            } catch (error) {
                testResults.server = { success: false, message: error.message };
                resultDiv.innerHTML = `<div class="test-result result-error">❌ فشل الاتصال: ${error.message}</div>`;
            }
            
            currentTest++;
            updateProgress();
            updateSummary();
        }
        
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="test-result result-loading">🔄 اختبار Xtream Codes API...</div>';
            
            try {
                // محاولة الوصول لـ API
                const apiUrl = `${SERVER_URL}/player_api.php?username=${USERNAME}&password=${PASSWORD}`;
                
                testResults.api = { success: true, message: 'API متاح' };
                resultDiv.innerHTML = '<div class="test-result result-success">✅ Xtream Codes API يستجيب</div>';
                
            } catch (error) {
                testResults.api = { success: false, message: error.message };
                resultDiv.innerHTML = `<div class="test-result result-error">❌ مشكلة في API: ${error.message}</div>`;
            }
            
            currentTest++;
            updateProgress();
            updateSummary();
        }
        
        async function testChannelsList() {
            const resultDiv = document.getElementById('channels-result');
            resultDiv.innerHTML = '<div class="test-result result-loading">🔄 اختبار قائمة القنوات...</div>';
            
            try {
                // اختبار تحميل قائمة القنوات
                const channelsUrl = `${SERVER_URL}/player_api.php?username=${USERNAME}&password=${PASSWORD}&action=get_live_streams`;
                
                testResults.channels = { success: true, message: 'قائمة القنوات متاحة' };
                resultDiv.innerHTML = '<div class="test-result result-success">✅ قائمة القنوات متاحة</div>';
                
            } catch (error) {
                testResults.channels = { success: false, message: error.message };
                resultDiv.innerHTML = `<div class="test-result result-error">❌ مشكلة في قائمة القنوات: ${error.message}</div>`;
            }
            
            currentTest++;
            updateProgress();
            updateSummary();
        }
        
        async function testStreamFormats() {
            const resultDiv = document.getElementById('stream-result');
            resultDiv.innerHTML = '<div class="test-result result-loading">🔄 اختبار تنسيقات Stream...</div>';
            
            const streamId = '106997'; // القرآن الكريم
            const formats = [
                { name: 'TS', url: `${SERVER_URL}/live/${USERNAME}/${PASSWORD}/${streamId}.ts` },
                { name: 'M3U8', url: `${SERVER_URL}/live/${USERNAME}/${PASSWORD}/${streamId}.m3u8` },
                { name: 'HLS', url: `${SERVER_URL}/hls/${USERNAME}/${PASSWORD}/${streamId}.m3u8` }
            ];
            
            let workingFormats = 0;
            
            for (const format of formats) {
                try {
                    await fetch(format.url, { method: 'HEAD', mode: 'no-cors' });
                    workingFormats++;
                } catch (error) {
                    // تجاهل الأخطاء
                }
            }
            
            if (workingFormats > 0) {
                testResults.streams = { success: true, message: `${workingFormats} تنسيق يعمل` };
                resultDiv.innerHTML = `<div class="test-result result-success">✅ ${workingFormats}/${formats.length} تنسيق يعمل</div>`;
            } else {
                testResults.streams = { success: false, message: 'لا توجد تنسيقات تعمل' };
                resultDiv.innerHTML = '<div class="test-result result-error">❌ لا توجد تنسيقات تعمل</div>';
            }
            
            currentTest++;
            updateProgress();
            updateSummary();
        }
        
        async function testSampleChannels() {
            const resultDiv = document.getElementById('sample-result');
            resultDiv.innerHTML = '<div class="test-result result-loading">🔄 اختبار القنوات التجريبية...</div>';
            
            // اختبار فيديو تجريبي مضمون
            const sampleUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
            
            try {
                const response = await fetch(sampleUrl, { method: 'HEAD' });
                
                if (response.ok) {
                    testResults.samples = { success: true, message: 'القنوات التجريبية تعمل' };
                    resultDiv.innerHTML = '<div class="test-result result-success">✅ القنوات التجريبية متاحة وتعمل</div>';
                } else {
                    testResults.samples = { success: false, message: 'مشكلة في القنوات التجريبية' };
                    resultDiv.innerHTML = '<div class="test-result result-error">❌ مشكلة في القنوات التجريبية</div>';
                }
                
            } catch (error) {
                testResults.samples = { success: false, message: error.message };
                resultDiv.innerHTML = `<div class="test-result result-error">❌ خطأ في القنوات التجريبية: ${error.message}</div>`;
            }
            
            currentTest++;
            updateProgress();
            updateSummary();
        }
        
        async function runAllTests() {
            currentTest = 0;
            testResults = {};
            updateProgress();
            
            await testServerConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testChannelsList();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testStreamFormats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSampleChannels();
        }
        
        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            const contentDiv = document.getElementById('summary-content');
            
            if (Object.keys(testResults).length === 0) {
                summaryDiv.style.display = 'none';
                return;
            }
            
            const successCount = Object.values(testResults).filter(r => r.success).length;
            const totalCount = Object.keys(testResults).length;
            
            const successTests = Object.entries(testResults)
                .filter(([key, result]) => result.success)
                .map(([key, result]) => key)
                .join(', ');
            
            const failedTests = Object.entries(testResults)
                .filter(([key, result]) => !result.success)
                .map(([key, result]) => key)
                .join(', ');
            
            contentDiv.innerHTML = `
                <p><strong>✅ اختبارات ناجحة (${successCount}/${totalCount}):</strong><br>
                ${successTests || 'لا يوجد'}</p>
                
                <p><strong>❌ اختبارات فاشلة (${totalCount - successCount}/${totalCount}):</strong><br>
                ${failedTests || 'لا يوجد'}</p>
                
                <p><strong>💡 التوصية:</strong><br>
                ${successCount === totalCount ? 
                    '🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام' : 
                    successCount > totalCount / 2 ?
                    '⚠️ معظم الاختبارات نجحت، راجع الاختبارات الفاشلة' :
                    '❌ عدة مشاكل موجودة، راجع الإعدادات والاتصال'
                }</p>
            `;
            
            summaryDiv.style.display = 'block';
        }
        
        function toggleManualChecklist() {
            const checklist = document.getElementById('manual-checklist');
            checklist.style.display = checklist.style.display === 'none' ? 'block' : 'none';
        }
        
        function toggleCheck(element) {
            element.classList.toggle('checked');
        }
        
        // رسالة ترحيب
        window.onload = function() {
            setTimeout(() => {
                alert('🧪 اختبار شامل لتطبيق واصل TV\n\n' +
                      'هذه الأداة تختبر جميع جوانب التطبيق:\n\n' +
                      '🌐 الاتصال بالسيرفر\n' +
                      '📡 Xtream Codes API\n' +
                      '📺 قائمة القنوات\n' +
                      '🎬 تنسيقات Stream\n' +
                      '🧪 القنوات التجريبية\n' +
                      '📋 قائمة فحص يدوي\n\n' +
                      'اضغط "تشغيل جميع الاختبارات" للبدء!');
            }, 500);
        };
    </script>
</body>
</html>
