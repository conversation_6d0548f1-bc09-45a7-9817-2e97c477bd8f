package com.waseltv.iptv.data.preferences

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.waseltv.iptv.data.model.ServerConfig
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

class PreferencesManager {
    
    companion object {
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")
        
        private val SERVER_URL_KEY = stringPreferencesKey("server_url")
        private val USERNAME_KEY = stringPreferencesKey("username")
        private val PASSWORD_KEY = stringPreferencesKey("password")
        private val PLAYLIST_URL_KEY = stringPreferencesKey("playlist_url")
    }
    
    // Note: These methods need a Context instance to work properly
    // They should be called with a context parameter or from an Application class

    suspend fun saveServerConfig(context: Context, serverConfig: ServerConfig) {
        context.dataStore.edit { preferences ->
            preferences[SERVER_URL_KEY] = serverConfig.serverUrl
            serverConfig.username?.let { preferences[USERNAME_KEY] = it }
            serverConfig.password?.let { preferences[PASSWORD_KEY] = it }
            serverConfig.playlistUrl?.let { preferences[PLAYLIST_URL_KEY] = it }
        }
    }

    suspend fun getServerConfig(context: Context): ServerConfig? {
        val preferences = context.dataStore.data.first()
        val serverUrl = preferences[SERVER_URL_KEY] ?: return null

        return ServerConfig(
            serverUrl = serverUrl,
            username = preferences[USERNAME_KEY],
            password = preferences[PASSWORD_KEY],
            playlistUrl = preferences[PLAYLIST_URL_KEY]
        )
    }

    fun getServerConfigFlow(context: Context): Flow<ServerConfig?> {
        return context.dataStore.data.map { preferences ->
            val serverUrl = preferences[SERVER_URL_KEY]
            if (serverUrl != null) {
                ServerConfig(
                    serverUrl = serverUrl,
                    username = preferences[USERNAME_KEY],
                    password = preferences[PASSWORD_KEY],
                    playlistUrl = preferences[PLAYLIST_URL_KEY]
                )
            } else {
                null
            }
        }
    }

    suspend fun clearServerConfig(context: Context) {
        context.dataStore.edit { preferences ->
            preferences.remove(SERVER_URL_KEY)
            preferences.remove(USERNAME_KEY)
            preferences.remove(PASSWORD_KEY)
            preferences.remove(PLAYLIST_URL_KEY)
        }
    }
}
