package com.waseltv.iptv.utils

import android.util.Log
import com.waseltv.iptv.data.model.Channel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.concurrent.TimeUnit

object ServerTester {
    private const val TAG = "WASEL-TV-ServerTester"
    private const val SERVER_URL = "http://maventv.one:80"
    private const val USERNAME = "Odaitv"
    private const val PASSWORD = "Odai2030"
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(15, TimeUnit.SECONDS)
        .readTimeout(15, TimeUnit.SECONDS)
        .followRedirects(true)
        .followSslRedirects(true)
        .build()
    
    suspend fun testAllEndpoints(): List<TestResult> = withContext(Dispatchers.IO) {
        val results = mutableListOf<TestResult>()
        
        val endpoints = listOf(
            "Basic Server" to SERVER_URL,
            "Player API" to "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD",
            "Live Streams" to "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_streams",
            "M3U Playlist" to "$SERVER_URL/get.php?username=$USERNAME&password=$PASSWORD&type=m3u",
            "M3U Plus" to "$SERVER_URL/get.php?username=$USERNAME&password=$PASSWORD&type=m3u_plus",
            "XMLTV" to "$SERVER_URL/xmltv.php?username=$USERNAME&password=$PASSWORD"
        )
        
        for ((name, url) in endpoints) {
            val result = testEndpoint(name, url)
            results.add(result)
            Log.d(TAG, "Test $name: ${result.status}")
        }
        
        return@withContext results
    }
    
    private suspend fun testEndpoint(name: String, url: String): TestResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Testing $name: $url")
            
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WASEL-TV/1.0")
                .addHeader("Accept", "*/*")
                .build()
            
            val response = httpClient.newCall(request).execute()
            val content = response.body?.string() ?: ""
            
            val status = when {
                !response.isSuccessful -> "Failed (${response.code})"
                content.contains("Invalid Authorization") -> "Auth Failed"
                content.contains("404 Error") -> "Not Found"
                content.isEmpty() -> "Empty Response"
                content.trim().startsWith("#EXTM3U") -> "M3U OK (${countM3UChannels(content)} channels)"
                content.trim().startsWith("[") || content.trim().startsWith("{") -> "JSON OK"
                else -> "Unknown Format"
            }
            
            TestResult(
                name = name,
                url = url,
                status = status,
                responseCode = response.code,
                contentLength = content.length,
                contentPreview = content.take(100),
                success = response.isSuccessful && 
                         !content.contains("Invalid Authorization") && 
                         !content.contains("404 Error")
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error testing $name", e)
            TestResult(
                name = name,
                url = url,
                status = "Error: ${e.message}",
                responseCode = -1,
                contentLength = 0,
                contentPreview = "",
                success = false
            )
        }
    }
    
    private fun countM3UChannels(content: String): Int {
        return content.lines().count { it.trim().startsWith("#EXTINF:") }
    }
    
    suspend fun testStreamUrls(): List<Channel> = withContext(Dispatchers.IO) {
        val testChannels = mutableListOf<Channel>()
        
        // جرب أرقام مختلفة للقنوات
        val testIds = listOf("1", "2", "3", "100", "1001", "1002", "10001")
        val formats = listOf(".ts", ".m3u8", "")
        
        for (id in testIds) {
            for (format in formats) {
                val streamUrl = "$SERVER_URL/live/$USERNAME/$PASSWORD/$id$format"
                
                try {
                    val request = Request.Builder()
                        .url(streamUrl)
                        .addHeader("User-Agent", "WASEL-TV/1.0")
                        .build()
                    
                    val response = httpClient.newCall(request).execute()
                    
                    if (response.isSuccessful) {
                        testChannels.add(Channel(
                            id = "${id}_${format.replace(".", "")}",
                            name = "Test Channel $id$format",
                            url = streamUrl,
                            logo = null,
                            group = "Test",
                            language = "Test",
                            country = "Test",
                            description = "Working stream found"
                        ))
                        
                        Log.d(TAG, "Working stream found: $streamUrl")
                        
                        // إذا وجدنا قناة تعمل، جرب المزيد بنفس التنسيق
                        if (testChannels.size >= 5) break
                    }
                    
                    response.close()
                } catch (e: Exception) {
                    Log.d(TAG, "Stream test failed for $streamUrl: ${e.message}")
                }
            }
            
            if (testChannels.size >= 5) break
        }
        
        return@withContext testChannels
    }
    
    data class TestResult(
        val name: String,
        val url: String,
        val status: String,
        val responseCode: Int,
        val contentLength: Int,
        val contentPreview: String,
        val success: Boolean
    )
}
