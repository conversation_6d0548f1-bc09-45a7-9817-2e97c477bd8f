// Simple Google Drive Integration for Diamond Eagles
// نظام Google Drive مبسط للنسور الماسية

console.log('🌐 تحميل نظام Google Drive المبسط...');

// Simple Google Drive Configuration
const SIMPLE_DRIVE_CONFIG = {
    CLIENT_ID: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
    SCOPES: 'https://www.googleapis.com/auth/drive.file',
    FOLDER_NAME: 'النسور الماسية - البيانات'
};

let isSimpleDriveReady = false;
let simpleDriveUser = null;

/**
 * تهيئة Google Drive API المبسط
 * Initialize Simple Google Drive API
 */
async function initSimpleGoogleDrive() {
    try {
        console.log('🔄 تهيئة Google Drive API...');
        
        // Load Google API
        if (typeof gapi === 'undefined') {
            await loadGoogleAPI();
        }
        
        // Initialize gapi
        await gapi.load('auth2', initAuth);
        await gapi.load('client', initClient);
        
        console.log('✅ تم تهيئة Google Drive بنجاح');
        isSimpleDriveReady = true;
        updateDriveStatus();
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة Google Drive:', error);
        updateDriveStatus(false, 'خطأ في التهيئة');
    }
}

/**
 * تحميل Google API
 * Load Google API
 */
function loadGoogleAPI() {
    return new Promise((resolve, reject) => {
        if (typeof gapi !== 'undefined') {
            resolve();
            return;
        }
        
        const script = document.createElement('script');
        script.src = 'https://apis.google.com/js/api.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

/**
 * تهيئة المصادقة
 * Initialize Authentication
 */
function initAuth() {
    return gapi.auth2.init({
        client_id: SIMPLE_DRIVE_CONFIG.CLIENT_ID,
        scope: SIMPLE_DRIVE_CONFIG.SCOPES
    });
}

/**
 * تهيئة العميل
 * Initialize Client
 */
function initClient() {
    return gapi.client.init({
        discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest']
    });
}

/**
 * تسجيل الدخول إلى Google Drive
 * Sign in to Google Drive
 */
async function signInToSimpleDrive() {
    try {
        console.log('🔐 تسجيل الدخول إلى Google Drive...');
        
        if (!isSimpleDriveReady) {
            await initSimpleGoogleDrive();
        }
        
        const authInstance = gapi.auth2.getAuthInstance();
        const user = await authInstance.signIn();
        
        simpleDriveUser = user;
        console.log('✅ تم تسجيل الدخول بنجاح');
        
        updateDriveStatus(true, user.getBasicProfile().getName());
        
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في تسجيل الدخول:', error);
        updateDriveStatus(false, 'فشل تسجيل الدخول');
        return false;
    }
}

/**
 * تحديث حالة Google Drive في الواجهة
 * Update Google Drive status in UI
 */
function updateDriveStatus(connected = false, userName = '') {
    const statusElement = document.getElementById('googleDriveStatus');
    const userElement = document.getElementById('googleDriveUser');
    const connectBtn = document.getElementById('googleDriveConnectBtn');
    const uploadBtn = document.getElementById('googleDriveUploadBtn');
    const downloadBtn = document.getElementById('googleDriveDownloadBtn');
    
    if (statusElement) {
        const statusText = statusElement.querySelector('.status-text');
        if (statusText) {
            if (connected) {
                statusText.textContent = 'متصل';
                statusText.className = 'status-text connected';
            } else {
                statusText.textContent = 'غير متصل';
                statusText.className = 'status-text offline';
            }
        }
    }
    
    if (userElement) {
        userElement.textContent = connected ? userName : 'غير متصل';
    }
    
    if (connectBtn) {
        connectBtn.textContent = connected ? 'تسجيل خروج' : 'تسجيل دخول Google';
        connectBtn.onclick = connected ? signOutFromSimpleDrive : signInToSimpleDrive;
    }
    
    if (uploadBtn) {
        uploadBtn.disabled = !connected;
    }
    
    if (downloadBtn) {
        downloadBtn.disabled = !connected;
    }
}

/**
 * تسجيل الخروج من Google Drive
 * Sign out from Google Drive
 */
async function signOutFromSimpleDrive() {
    try {
        const authInstance = gapi.auth2.getAuthInstance();
        await authInstance.signOut();
        
        simpleDriveUser = null;
        console.log('✅ تم تسجيل الخروج');
        
        updateDriveStatus(false);
        
    } catch (error) {
        console.error('❌ خطأ في تسجيل الخروج:', error);
    }
}

/**
 * رفع البيانات إلى Google Drive
 * Upload data to Google Drive
 */
async function uploadToSimpleDrive() {
    try {
        if (!simpleDriveUser) {
            alert('يرجى تسجيل الدخول أولاً');
            return;
        }
        
        console.log('📤 رفع البيانات إلى Google Drive...');
        
        // Prepare data
        const data = {
            products: products || [],
            customers: customers || [],
            timestamp: new Date().toISOString(),
            version: '1.0'
        };
        
        const dataBlob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        
        // Upload to Drive
        const metadata = {
            name: `النسور_الماسية_البيانات_${new Date().toISOString().split('T')[0]}.json`,
            parents: await getOrCreateFolder()
        };
        
        const form = new FormData();
        form.append('metadata', new Blob([JSON.stringify(metadata)], {type: 'application/json'}));
        form.append('file', dataBlob);
        
        const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
            method: 'POST',
            headers: new Headers({
                'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
            }),
            body: form
        });
        
        if (response.ok) {
            console.log('✅ تم رفع البيانات بنجاح');
            updateLastSyncTime();
            showNotification('تم رفع البيانات بنجاح', 'success');
        } else {
            throw new Error('فشل في رفع البيانات');
        }
        
    } catch (error) {
        console.error('❌ خطأ في رفع البيانات:', error);
        showNotification('فشل في رفع البيانات', 'error');
    }
}

/**
 * تحميل البيانات من Google Drive
 * Download data from Google Drive
 */
async function downloadFromSimpleDrive() {
    try {
        if (!simpleDriveUser) {
            alert('يرجى تسجيل الدخول أولاً');
            return;
        }
        
        console.log('📥 تحميل البيانات من Google Drive...');
        
        // Find latest file
        const folderId = await getOrCreateFolder();
        const response = await gapi.client.drive.files.list({
            q: `'${folderId}' in parents and name contains 'النسور_الماسية_البيانات'`,
            orderBy: 'createdTime desc',
            pageSize: 1
        });
        
        if (response.result.files.length === 0) {
            showNotification('لا توجد بيانات في Google Drive', 'warning');
            return;
        }
        
        const fileId = response.result.files[0].id;
        const fileResponse = await gapi.client.drive.files.get({
            fileId: fileId,
            alt: 'media'
        });
        
        const data = JSON.parse(fileResponse.body);
        
        // Update local data
        if (data.products) {
            products = data.products;
            localStorage.setItem('products', JSON.stringify(products));
        }
        
        if (data.customers) {
            customers = data.customers;
            localStorage.setItem('customers', JSON.stringify(customers));
        }
        
        // Update UI
        if (typeof displayProducts === 'function') displayProducts();
        if (typeof displayCustomers === 'function') displayCustomers();
        if (typeof updateDashboard === 'function') updateDashboard();
        
        console.log('✅ تم تحميل البيانات بنجاح');
        updateLastSyncTime();
        showNotification('تم تحميل البيانات بنجاح', 'success');
        
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        showNotification('فشل في تحميل البيانات', 'error');
    }
}

/**
 * الحصول على مجلد البيانات أو إنشاؤه
 * Get or create data folder
 */
async function getOrCreateFolder() {
    try {
        // Search for existing folder
        const response = await gapi.client.drive.files.list({
            q: `name='${SIMPLE_DRIVE_CONFIG.FOLDER_NAME}' and mimeType='application/vnd.google-apps.folder'`,
            pageSize: 1
        });
        
        if (response.result.files.length > 0) {
            return [response.result.files[0].id];
        }
        
        // Create new folder
        const folderResponse = await gapi.client.drive.files.create({
            resource: {
                name: SIMPLE_DRIVE_CONFIG.FOLDER_NAME,
                mimeType: 'application/vnd.google-apps.folder'
            }
        });
        
        return [folderResponse.result.id];
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء المجلد:', error);
        return [];
    }
}

/**
 * تحديث وقت آخر مزامنة
 * Update last sync time
 */
function updateLastSyncTime() {
    const now = new Date().toLocaleString('ar-SA');
    const syncElement = document.getElementById('lastGoogleDriveSync');
    if (syncElement) {
        syncElement.textContent = now;
    }
    localStorage.setItem('lastGoogleDriveSync', now);
}

/**
 * عرض إشعار
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Use existing notification system or create simple alert
    if (typeof showSyncNotification === 'function') {
        showSyncNotification(message, type);
    } else {
        alert(message);
    }
}

// Override existing functions
window.connectToGoogleDrive = signInToSimpleDrive;
window.uploadAllToGoogleDrive = uploadToSimpleDrive;
window.downloadAllFromGoogleDrive = downloadFromSimpleDrive;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initSimpleGoogleDrive();
    }, 2000);
});

console.log('✅ تم تحميل نظام Google Drive المبسط');
