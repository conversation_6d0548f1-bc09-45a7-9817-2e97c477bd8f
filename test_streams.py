#!/usr/bin/env python3
"""
أداة لاختبار stream URLs من سيرفر Xtream Codes
"""

import requests
import json
import time

# معلومات السيرفر
SERVER_URL = "http://maventv.one:80"
USERNAME = "odaitv"
PASSWORD = "Odai2030"

def test_stream_url(stream_id):
    """اختبار رابط stream محدد"""
    stream_url = f"{SERVER_URL}/live/{USERNAME}/{PASSWORD}/{stream_id}.ts"
    
    try:
        print(f"🔗 اختبار Stream ID: {stream_id}")
        print(f"   URL: {stream_url}")
        
        response = requests.head(stream_url, timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ يعمل! (Status: {response.status_code})")
            content_type = response.headers.get('content-type', 'غير محدد')
            content_length = response.headers.get('content-length', 'غير محدد')
            print(f"   📄 Content-Type: {content_type}")
            print(f"   📏 Content-Length: {content_length}")
            return True
        else:
            print(f"   ❌ لا يعمل (Status: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def get_live_streams():
    """الحصول على قائمة القنوات المباشرة"""
    url = f"{SERVER_URL}/player_api.php?username={USERNAME}&password={PASSWORD}&action=get_live_streams"
    
    try:
        print("📡 جاري الحصول على قائمة القنوات...")
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ تم تحميل {len(data)} قناة")
            return data
        else:
            print(f"❌ فشل في تحميل القنوات: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ خطأ في تحميل القنوات: {str(e)}")
        return []

def test_sample_streams():
    """اختبار عينة من القنوات"""
    print("🎯 اختبار عينة من stream URLs...")
    
    # قائمة stream IDs للاختبار
    test_ids = [
        "106997",  # القرآن الكريم
        "107001",  # قناة تجريبية
        "107002",  # قناة تجريبية
        "1", "2", "3", "4", "5",  # أرقام بسيطة
        "100", "101", "102",      # أرقام متوسطة
        "1000", "1001", "1002"    # أرقام كبيرة
    ]
    
    working_streams = []
    
    for stream_id in test_ids:
        if test_stream_url(stream_id):
            working_streams.append(stream_id)
        time.sleep(1)  # تأخير بسيط بين الطلبات
        print()
    
    print(f"📊 النتائج:")
    print(f"   ✅ Streams تعمل: {len(working_streams)}")
    print(f"   ❌ Streams لا تعمل: {len(test_ids) - len(working_streams)}")
    
    if working_streams:
        print(f"   🎯 Stream IDs التي تعمل: {', '.join(working_streams)}")
    
    return working_streams

def analyze_channels():
    """تحليل القنوات المتاحة"""
    channels = get_live_streams()
    
    if not channels:
        print("❌ لا توجد قنوات للتحليل")
        return
    
    print(f"\n📊 تحليل {len(channels)} قناة:")
    
    # تحليل الفئات
    categories = {}
    arabic_channels = []
    
    for channel in channels[:50]:  # تحليل أول 50 قناة فقط
        name = channel.get('name', '')
        category_id = channel.get('category_id', '')
        stream_id = channel.get('stream_id', '')
        
        # تجميع الفئات
        if category_id:
            if category_id not in categories:
                categories[category_id] = []
            categories[category_id].append(name)
        
        # البحث عن القنوات العربية
        if any(arabic_word in name.lower() for arabic_word in ['العربية', 'الجزيرة', 'mbc', 'دبي', 'أبو ظبي']):
            arabic_channels.append({
                'name': name,
                'id': stream_id,
                'category': category_id
            })
    
    print(f"\n📂 الفئات المتاحة:")
    for cat_id, channels_in_cat in categories.items():
        print(f"   {cat_id}: {len(channels_in_cat)} قناة")
    
    print(f"\n🇸🇦 القنوات العربية المكتشفة ({len(arabic_channels)}):")
    for channel in arabic_channels[:10]:  # أول 10 قنوات عربية
        print(f"   {channel['id']}: {channel['name']} (فئة: {channel['category']})")
    
    # اختبار بعض القنوات العربية
    if arabic_channels:
        print(f"\n🔍 اختبار القنوات العربية:")
        working_arabic = []
        for channel in arabic_channels[:5]:  # اختبار أول 5 قنوات
            if test_stream_url(channel['id']):
                working_arabic.append(channel)
        
        if working_arabic:
            print(f"\n✅ القنوات العربية التي تعمل:")
            for channel in working_arabic:
                print(f"   {channel['id']}: {channel['name']}")

if __name__ == "__main__":
    print("🚀 بدء اختبار Xtream Codes Streams")
    print("=" * 50)
    
    # اختبار عينة من الـ streams
    working_streams = test_sample_streams()
    
    print("\n" + "=" * 50)
    
    # تحليل القنوات المتاحة
    analyze_channels()
    
    print("\n✅ انتهى الاختبار")
