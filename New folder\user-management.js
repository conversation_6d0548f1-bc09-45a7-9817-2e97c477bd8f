// User Management System for Diamond Eagles Inventory
// نظام إدارة المستخدمين لتطبيق النسور الماسية

console.log('🔐 تحميل نظام إدارة المستخدمين...');

// User roles and permissions
const USER_ROLES = {
    ADMIN: 'admin',
    MANAGER: 'manager',
    EMPLOYEE: 'employee',
    VIEWER: 'viewer'
};

const PERMISSIONS = {
    VIEW_DASHBOARD: 'view_dashboard',
    VIEW_PRODUCTS: 'view_products',
    ADD_PRODUCTS: 'add_products',
    EDIT_PRODUCTS: 'edit_products',
    DELETE_PRODUCTS: 'delete_products',
    VIEW_CUSTOMERS: 'view_customers',
    ADD_CUSTOMERS: 'add_customers',
    EDIT_CUSTOMERS: 'edit_customers',
    DELETE_CUSTOMERS: 'delete_customers',
    VIEW_SETTINGS: 'view_settings',
    EDIT_SETTINGS: 'edit_settings',
    MANAGE_USERS: 'manage_users'
};

// Role permissions mapping
const ROLE_PERMISSIONS = {
    [USER_ROLES.ADMIN]: Object.values(PERMISSIONS),
    [USER_ROLES.MANAGER]: [
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.VIEW_PRODUCTS,
        PERMISSIONS.ADD_PRODUCTS,
        PERMISSIONS.EDIT_PRODUCTS,
        PERMISSIONS.VIEW_CUSTOMERS,
        PERMISSIONS.ADD_CUSTOMERS,
        PERMISSIONS.EDIT_CUSTOMERS,
        PERMISSIONS.VIEW_SETTINGS
    ],
    [USER_ROLES.EMPLOYEE]: [
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.VIEW_PRODUCTS,
        PERMISSIONS.ADD_PRODUCTS,
        PERMISSIONS.VIEW_CUSTOMERS,
        PERMISSIONS.ADD_CUSTOMERS
    ],
    [USER_ROLES.VIEWER]: [
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.VIEW_PRODUCTS,
        PERMISSIONS.VIEW_CUSTOMERS
    ]
};

// Global users array
let systemUsers = [];

// User Management Class
class UserManager {
    constructor() {
        this.currentUser = null;
        this.loadUsers();
        this.loadCurrentUser();
    }

    // Load users from localStorage
    loadUsers() {
        try {
            const savedUsers = localStorage.getItem('systemUsers');
            if (savedUsers) {
                systemUsers = JSON.parse(savedUsers);
            } else {
                // Initialize with default admin user
                systemUsers = [{
                    id: this.generateUserId(),
                    name: 'المدير الرئيسي',
                    email: '<EMAIL>',
                    password: '2030',
                    role: USER_ROLES.ADMIN,
                    permissions: ROLE_PERMISSIONS[USER_ROLES.ADMIN],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                }];
                this.saveUsers();
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل المستخدمين:', error);
            systemUsers = [];
        }
    }

    // Save users to localStorage
    saveUsers() {
        try {
            localStorage.setItem('systemUsers', JSON.stringify(systemUsers));
            console.log('✅ تم حفظ بيانات المستخدمين');
        } catch (error) {
            console.error('❌ خطأ في حفظ المستخدمين:', error);
        }
    }

    // Load current user
    loadCurrentUser() {
        try {
            const currentUserData = localStorage.getItem('currentUser');
            if (currentUserData) {
                this.currentUser = JSON.parse(currentUserData);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل المستخدم الحالي:', error);
        }
    }

    // Save current user
    saveCurrentUser() {
        try {
            if (this.currentUser) {
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
            }
        } catch (error) {
            console.error('❌ خطأ في حفظ المستخدم الحالي:', error);
        }
    }

    // Generate unique user ID
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Add new user
    addUser(userData) {
        try {
            // Validate required fields
            if (!userData.name || !userData.email || !userData.password || !userData.role) {
                throw new Error('جميع الحقول مطلوبة');
            }

            // Check if email already exists
            const existingUser = systemUsers.find(user => user.email === userData.email);
            if (existingUser) {
                throw new Error('البريد الإلكتروني مسجل مسبقاً');
            }

            // Create new user
            const newUser = {
                id: this.generateUserId(),
                name: userData.name.trim(),
                email: userData.email.trim().toLowerCase(),
                password: userData.password,
                role: userData.role,
                permissions: userData.permissions || ROLE_PERMISSIONS[userData.role] || [],
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null
            };

            systemUsers.push(newUser);
            this.saveUsers();

            console.log('✅ تم إضافة المستخدم بنجاح:', newUser.name);
            return { success: true, user: newUser };

        } catch (error) {
            console.error('❌ خطأ في إضافة المستخدم:', error);
            return { success: false, error: error.message };
        }
    }

    // Update user
    updateUser(userId, updateData) {
        try {
            const userIndex = systemUsers.findIndex(user => user.id === userId);
            if (userIndex === -1) {
                throw new Error('المستخدم غير موجود');
            }

            // Update user data
            systemUsers[userIndex] = {
                ...systemUsers[userIndex],
                ...updateData,
                updatedAt: new Date().toISOString()
            };

            this.saveUsers();

            console.log('✅ تم تحديث المستخدم بنجاح');
            return { success: true, user: systemUsers[userIndex] };

        } catch (error) {
            console.error('❌ خطأ في تحديث المستخدم:', error);
            return { success: false, error: error.message };
        }
    }

    // Delete user
    deleteUser(userId) {
        try {
            const userIndex = systemUsers.findIndex(user => user.id === userId);
            if (userIndex === -1) {
                throw new Error('المستخدم غير موجود');
            }

            // Don't allow deleting the last admin
            const user = systemUsers[userIndex];
            if (user.role === USER_ROLES.ADMIN) {
                const adminCount = systemUsers.filter(u => u.role === USER_ROLES.ADMIN && u.isActive).length;
                if (adminCount <= 1) {
                    throw new Error('لا يمكن حذف آخر مدير في النظام');
                }
            }

            systemUsers.splice(userIndex, 1);
            this.saveUsers();

            console.log('✅ تم حذف المستخدم بنجاح');
            return { success: true };

        } catch (error) {
            console.error('❌ خطأ في حذف المستخدم:', error);
            return { success: false, error: error.message };
        }
    }

    // Authenticate user
    authenticateUser(email, password) {
        try {
            const user = systemUsers.find(u => 
                u.email === email.toLowerCase() && 
                u.password === password && 
                u.isActive
            );

            if (user) {
                // Update last login
                user.lastLogin = new Date().toISOString();
                this.currentUser = user;
                this.saveUsers();
                this.saveCurrentUser();

                console.log('✅ تم تسجيل الدخول بنجاح:', user.name);
                return { success: true, user: user };
            } else {
                throw new Error('بيانات تسجيل الدخول غير صحيحة');
            }

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            return { success: false, error: error.message };
        }
    }

    // Check if user has permission
    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes(permission);
    }

    // Get all users
    getAllUsers() {
        return systemUsers.filter(user => user.isActive);
    }

    // Get user by ID
    getUserById(userId) {
        return systemUsers.find(user => user.id === userId);
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Logout current user
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        console.log('✅ تم تسجيل الخروج بنجاح');
    }
}

// Global user manager instance
const userManager = new UserManager();

// Export for global use
window.userManager = userManager;
window.USER_ROLES = USER_ROLES;
window.PERMISSIONS = PERMISSIONS;
window.ROLE_PERMISSIONS = ROLE_PERMISSIONS;

// UI Functions for User Management
// وظائف واجهة المستخدم لإدارة المستخدمين

/**
 * Show add user modal
 */
function showAddUserModal() {
    const modalHtml = `
        <div class="modal active" id="addUserModal">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h2><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</h2>
                    <span class="close" onclick="closeModal('addUserModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addUserForm" onsubmit="addNewUser(event)">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="userName">الاسم الكامل *</label>
                                <input type="text" id="userName" name="userName" required placeholder="أدخل الاسم الكامل">
                            </div>
                            <div class="form-group">
                                <label for="userEmail">البريد الإلكتروني *</label>
                                <input type="email" id="userEmail" name="userEmail" required placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="userPassword">كلمة المرور *</label>
                                <input type="password" id="userPassword" name="userPassword" required placeholder="كلمة مرور قوية">
                            </div>
                            <div class="form-group">
                                <label for="userRole">الدور *</label>
                                <select id="userRole" name="userRole" required onchange="updatePermissionsPreview()">
                                    <option value="">اختر الدور</option>
                                    <option value="admin">مدير (صلاحيات كاملة)</option>
                                    <option value="manager">مدير قسم (صلاحيات محدودة)</option>
                                    <option value="employee">موظف (إضافة وعرض)</option>
                                    <option value="viewer">مشاهد (عرض فقط)</option>
                                </select>
                            </div>
                        </div>

                        <div class="permissions-preview" id="permissionsPreview" style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px; display: none;">
                            <h4><i class="fas fa-key"></i> الصلاحيات المتاحة:</h4>
                            <div id="permissionsList"></div>
                        </div>

                        <div class="custom-permissions" style="margin: 20px 0;">
                            <label>
                                <input type="checkbox" id="customPermissions" onchange="toggleCustomPermissions()">
                                تخصيص الصلاحيات يدوياً
                            </label>
                            <div id="customPermissionsContainer" style="display: none; margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">
                                <div class="permissions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                                    <label><input type="checkbox" name="permissions" value="view_dashboard"> عرض لوحة التحكم</label>
                                    <label><input type="checkbox" name="permissions" value="view_products"> عرض المنتجات</label>
                                    <label><input type="checkbox" name="permissions" value="add_products"> إضافة منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="edit_products"> تعديل منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="delete_products"> حذف منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="view_customers"> عرض العملاء</label>
                                    <label><input type="checkbox" name="permissions" value="add_customers"> إضافة عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="edit_customers"> تعديل عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="delete_customers"> حذف عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="view_settings"> عرض الإعدادات</label>
                                    <label><input type="checkbox" name="permissions" value="edit_settings"> تعديل الإعدادات</label>
                                    <label><input type="checkbox" name="permissions" value="manage_users"> إدارة المستخدمين</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> إضافة المستخدم
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addUserModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

/**
 * Update permissions preview based on selected role
 */
function updatePermissionsPreview() {
    const roleSelect = document.getElementById('userRole');
    const previewDiv = document.getElementById('permissionsPreview');
    const permissionsList = document.getElementById('permissionsList');

    if (roleSelect.value) {
        const permissions = ROLE_PERMISSIONS[roleSelect.value] || [];
        const permissionNames = {
            'view_dashboard': 'عرض لوحة التحكم',
            'view_products': 'عرض المنتجات',
            'add_products': 'إضافة منتجات',
            'edit_products': 'تعديل منتجات',
            'delete_products': 'حذف منتجات',
            'view_customers': 'عرض العملاء',
            'add_customers': 'إضافة عملاء',
            'edit_customers': 'تعديل عملاء',
            'delete_customers': 'حذف عملاء',
            'view_settings': 'عرض الإعدادات',
            'edit_settings': 'تعديل الإعدادات',
            'manage_users': 'إدارة المستخدمين'
        };

        const permissionsHtml = permissions.map(permission =>
            `<span class="permission-badge" style="display: inline-block; background: #e3f2fd; color: #1976d2; padding: 4px 8px; margin: 2px; border-radius: 4px; font-size: 12px;">
                <i class="fas fa-check"></i> ${permissionNames[permission] || permission}
            </span>`
        ).join('');

        permissionsList.innerHTML = permissionsHtml;
        previewDiv.style.display = 'block';
    } else {
        previewDiv.style.display = 'none';
    }
}

/**
 * Toggle custom permissions section
 */
function toggleCustomPermissions() {
    const checkbox = document.getElementById('customPermissions');
    const container = document.getElementById('customPermissionsContainer');
    const roleSelect = document.getElementById('userRole');

    if (checkbox.checked) {
        container.style.display = 'block';
        roleSelect.disabled = true;

        // Pre-select permissions based on current role
        if (roleSelect.value) {
            const permissions = ROLE_PERMISSIONS[roleSelect.value] || [];
            const checkboxes = container.querySelectorAll('input[name="permissions"]');
            checkboxes.forEach(cb => {
                cb.checked = permissions.includes(cb.value);
            });
        }
    } else {
        container.style.display = 'none';
        roleSelect.disabled = false;
    }
}

/**
 * Add new user
 */
function addNewUser(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const customPermissionsEnabled = document.getElementById('customPermissions').checked;

    let permissions;
    if (customPermissionsEnabled) {
        // Get custom permissions
        const selectedPermissions = Array.from(document.querySelectorAll('input[name="permissions"]:checked'))
            .map(cb => cb.value);
        permissions = selectedPermissions;
    } else {
        // Use role-based permissions
        permissions = ROLE_PERMISSIONS[formData.get('userRole')] || [];
    }

    const userData = {
        name: formData.get('userName'),
        email: formData.get('userEmail'),
        password: formData.get('userPassword'),
        role: formData.get('userRole'),
        permissions: permissions
    };

    const result = userManager.addUser(userData);

    if (result.success) {
        closeModal('addUserModal');
        loadUsersTable();
        if (typeof showToast === 'function') {
            showToast('تم إضافة المستخدم بنجاح', 'success');
        }
    } else {
        if (typeof showToast === 'function') {
            showToast('خطأ: ' + result.error, 'error');
        }
    }
}

/**
 * Load users table
 */
function loadUsersTable() {
    const tableBody = document.getElementById('usersTableBody');
    if (!tableBody) return;

    const users = userManager.getAllUsers();

    if (users.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                    <i class="fas fa-users"></i><br>
                    لا يوجد مستخدمون مسجلون
                </td>
            </tr>
        `;
        return;
    }

    const roleNames = {
        'admin': 'مدير',
        'manager': 'مدير قسم',
        'employee': 'موظف',
        'viewer': 'مشاهد'
    };

    tableBody.innerHTML = users.map(user => {
        const permissionsCount = user.permissions.length;
        const lastLogin = user.lastLogin ?
            new Date(user.lastLogin).toLocaleDateString('ar-SA') : 'لم يسجل دخول';

        return `
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">${user.name}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">${user.email}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <span class="role-badge role-${user.role}" style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        ${roleNames[user.role] || user.role}
                    </span>
                </td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <span class="permissions-count" style="background: #f3e5f5; color: #7b1fa2; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        ${permissionsCount} صلاحية
                    </span>
                </td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <span class="status-badge ${user.isActive ? 'status-active' : 'status-inactive'}"
                          style="background: ${user.isActive ? '#e8f5e8' : '#ffebee'};
                                 color: ${user.isActive ? '#2e7d32' : '#c62828'};
                                 padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        <i class="fas ${user.isActive ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                        ${user.isActive ? 'نشط' : 'معطل'}
                    </span>
                    <br><small style="color: #666;">آخر دخول: ${lastLogin}</small>
                </td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <div class="action-buttons" style="display: flex; gap: 5px;">
                        <button class="btn btn-sm btn-info" onclick="viewUserDetails('${user.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editUser('${user.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${user.role !== 'admin' ? `
                            <button class="btn btn-sm btn-danger" onclick="deleteUser('${user.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * View user details
 */
function viewUserDetails(userId) {
    const user = userManager.getUserById(userId);
    if (!user) return;

    const permissionNames = {
        'view_dashboard': 'عرض لوحة التحكم',
        'view_products': 'عرض المنتجات',
        'add_products': 'إضافة منتجات',
        'edit_products': 'تعديل منتجات',
        'delete_products': 'حذف منتجات',
        'view_customers': 'عرض العملاء',
        'add_customers': 'إضافة عملاء',
        'edit_customers': 'تعديل عملاء',
        'delete_customers': 'حذف عملاء',
        'view_settings': 'عرض الإعدادات',
        'edit_settings': 'تعديل الإعدادات',
        'manage_users': 'إدارة المستخدمين'
    };

    const permissionsHtml = user.permissions.map(permission =>
        `<li style="margin: 5px 0;"><i class="fas fa-check" style="color: #4caf50;"></i> ${permissionNames[permission] || permission}</li>`
    ).join('');

    const modalHtml = `
        <div class="modal active" id="userDetailsModal">
            <div class="modal-content medium-modal">
                <div class="modal-header">
                    <h2><i class="fas fa-user"></i> تفاصيل المستخدم</h2>
                    <span class="close" onclick="closeModal('userDetailsModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="user-details">
                        <div class="detail-item">
                            <strong>الاسم:</strong> ${user.name}
                        </div>
                        <div class="detail-item">
                            <strong>البريد الإلكتروني:</strong> ${user.email}
                        </div>
                        <div class="detail-item">
                            <strong>الدور:</strong> ${user.role}
                        </div>
                        <div class="detail-item">
                            <strong>تاريخ الإنشاء:</strong> ${new Date(user.createdAt).toLocaleDateString('ar-SA')}
                        </div>
                        <div class="detail-item">
                            <strong>آخر تسجيل دخول:</strong> ${user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar-SA') : 'لم يسجل دخول'}
                        </div>
                        <div class="detail-item">
                            <strong>الحالة:</strong>
                            <span style="color: ${user.isActive ? '#4caf50' : '#f44336'};">
                                ${user.isActive ? 'نشط' : 'معطل'}
                            </span>
                        </div>
                        <div class="detail-item">
                            <strong>الصلاحيات:</strong>
                            <ul style="margin: 10px 0; padding-right: 20px;">
                                ${permissionsHtml}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal('userDetailsModal')">إغلاق</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

/**
 * Edit user
 */
function editUser(userId) {
    const user = userManager.getUserById(userId);
    if (!user) return;

    const modalHtml = `
        <div class="modal active" id="editUserModal">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h2><i class="fas fa-user-edit"></i> تعديل المستخدم</h2>
                    <span class="close" onclick="closeModal('editUserModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="editUserForm" onsubmit="updateUser(event, '${userId}')">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="editUserName">الاسم الكامل *</label>
                                <input type="text" id="editUserName" name="userName" value="${user.name}" required>
                            </div>
                            <div class="form-group">
                                <label for="editUserEmail">البريد الإلكتروني *</label>
                                <input type="email" id="editUserEmail" name="userEmail" value="${user.email}" required>
                            </div>
                            <div class="form-group">
                                <label for="editUserPassword">كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية)</label>
                                <input type="password" id="editUserPassword" name="userPassword" placeholder="كلمة مرور جديدة">
                            </div>
                            <div class="form-group">
                                <label for="editUserRole">الدور *</label>
                                <select id="editUserRole" name="userRole" required>
                                    <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>مدير (صلاحيات كاملة)</option>
                                    <option value="manager" ${user.role === 'manager' ? 'selected' : ''}>مدير قسم (صلاحيات محدودة)</option>
                                    <option value="employee" ${user.role === 'employee' ? 'selected' : ''}>موظف (إضافة وعرض)</option>
                                    <option value="viewer" ${user.role === 'viewer' ? 'selected' : ''}>مشاهد (عرض فقط)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="editUserStatus">الحالة</label>
                                <select id="editUserStatus" name="userStatus">
                                    <option value="true" ${user.isActive ? 'selected' : ''}>نشط</option>
                                    <option value="false" ${!user.isActive ? 'selected' : ''}>معطل</option>
                                </select>
                            </div>
                        </div>

                        <div class="custom-permissions" style="margin: 20px 0;">
                            <label>
                                <input type="checkbox" id="editCustomPermissions" onchange="toggleEditCustomPermissions()">
                                تخصيص الصلاحيات يدوياً
                            </label>
                            <div id="editCustomPermissionsContainer" style="display: none; margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">
                                <div class="permissions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                                    <label><input type="checkbox" name="permissions" value="view_dashboard" ${user.permissions.includes('view_dashboard') ? 'checked' : ''}> عرض لوحة التحكم</label>
                                    <label><input type="checkbox" name="permissions" value="view_products" ${user.permissions.includes('view_products') ? 'checked' : ''}> عرض المنتجات</label>
                                    <label><input type="checkbox" name="permissions" value="add_products" ${user.permissions.includes('add_products') ? 'checked' : ''}> إضافة منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="edit_products" ${user.permissions.includes('edit_products') ? 'checked' : ''}> تعديل منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="delete_products" ${user.permissions.includes('delete_products') ? 'checked' : ''}> حذف منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="view_customers" ${user.permissions.includes('view_customers') ? 'checked' : ''}> عرض العملاء</label>
                                    <label><input type="checkbox" name="permissions" value="add_customers" ${user.permissions.includes('add_customers') ? 'checked' : ''}> إضافة عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="edit_customers" ${user.permissions.includes('edit_customers') ? 'checked' : ''}> تعديل عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="delete_customers" ${user.permissions.includes('delete_customers') ? 'checked' : ''}> حذف عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="view_settings" ${user.permissions.includes('view_settings') ? 'checked' : ''}> عرض الإعدادات</label>
                                    <label><input type="checkbox" name="permissions" value="edit_settings" ${user.permissions.includes('edit_settings') ? 'checked' : ''}> تعديل الإعدادات</label>
                                    <label><input type="checkbox" name="permissions" value="manage_users" ${user.permissions.includes('manage_users') ? 'checked' : ''}> إدارة المستخدمين</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="closeModal('editUserModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

/**
 * Toggle custom permissions in edit modal
 */
function toggleEditCustomPermissions() {
    const checkbox = document.getElementById('editCustomPermissions');
    const container = document.getElementById('editCustomPermissionsContainer');
    const roleSelect = document.getElementById('editUserRole');

    if (checkbox.checked) {
        container.style.display = 'block';
        roleSelect.disabled = true;
    } else {
        container.style.display = 'none';
        roleSelect.disabled = false;
    }
}

/**
 * Update user
 */
function updateUser(event, userId) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const customPermissionsEnabled = document.getElementById('editCustomPermissions').checked;

    let updateData = {
        name: formData.get('userName'),
        email: formData.get('userEmail'),
        role: formData.get('userRole'),
        isActive: formData.get('userStatus') === 'true'
    };

    // Update password only if provided
    const newPassword = formData.get('userPassword');
    if (newPassword && newPassword.trim()) {
        updateData.password = newPassword.trim();
    }

    // Update permissions
    if (customPermissionsEnabled) {
        const selectedPermissions = Array.from(document.querySelectorAll('#editCustomPermissionsContainer input[name="permissions"]:checked'))
            .map(cb => cb.value);
        updateData.permissions = selectedPermissions;
    } else {
        updateData.permissions = ROLE_PERMISSIONS[updateData.role] || [];
    }

    const result = userManager.updateUser(userId, updateData);

    if (result.success) {
        closeModal('editUserModal');
        loadUsersTable();
        if (typeof showToast === 'function') {
            showToast('تم تحديث المستخدم بنجاح', 'success');
        }
    } else {
        if (typeof showToast === 'function') {
            showToast('خطأ: ' + result.error, 'error');
        }
    }
}

/**
 * Delete user
 */
function deleteUser(userId) {
    const user = userManager.getUserById(userId);
    if (!user) return;

    if (confirm(`هل أنت متأكد من حذف المستخدم "${user.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        const result = userManager.deleteUser(userId);

        if (result.success) {
            loadUsersTable();
            if (typeof showToast === 'function') {
                showToast('تم حذف المستخدم بنجاح', 'success');
            }
        } else {
            if (typeof showToast === 'function') {
                showToast('خطأ: ' + result.error, 'error');
            }
        }
    }
}

/**
 * Initialize users table on page load
 */
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        loadUsersTable();
    }, 1000);
});

console.log('✅ تم تحميل نظام إدارة المستخدمين بنجاح');
