// User Management System for Diamond Eagles Inventory
// نظام إدارة المستخدمين لتطبيق النسور الماسية

console.log('🔐 تحميل نظام إدارة المستخدمين...');

// User roles and permissions
const USER_ROLES = {
    ADMIN: 'admin',
    MANAGER: 'manager',
    EMPLOYEE: 'employee',
    VIEWER: 'viewer'
};

const PERMISSIONS = {
    VIEW_DASHBOARD: 'view_dashboard',
    VIEW_PRODUCTS: 'view_products',
    ADD_PRODUCTS: 'add_products',
    EDIT_PRODUCTS: 'edit_products',
    DELETE_PRODUCTS: 'delete_products',
    VIEW_CUSTOMERS: 'view_customers',
    ADD_CUSTOMERS: 'add_customers',
    EDIT_CUSTOMERS: 'edit_customers',
    DELETE_CUSTOMERS: 'delete_customers',
    VIEW_SETTINGS: 'view_settings',
    EDIT_SETTINGS: 'edit_settings',
    MANAGE_USERS: 'manage_users'
};

// Role permissions mapping
const ROLE_PERMISSIONS = {
    [USER_ROLES.ADMIN]: Object.values(PERMISSIONS),
    [USER_ROLES.MANAGER]: [
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.VIEW_PRODUCTS,
        PERMISSIONS.ADD_PRODUCTS,
        PERMISSIONS.EDIT_PRODUCTS,
        PERMISSIONS.VIEW_CUSTOMERS,
        PERMISSIONS.ADD_CUSTOMERS,
        PERMISSIONS.EDIT_CUSTOMERS,
        PERMISSIONS.VIEW_SETTINGS
    ],
    [USER_ROLES.EMPLOYEE]: [
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.VIEW_PRODUCTS,
        PERMISSIONS.ADD_PRODUCTS,
        PERMISSIONS.VIEW_CUSTOMERS,
        PERMISSIONS.ADD_CUSTOMERS
    ],
    [USER_ROLES.VIEWER]: [
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.VIEW_PRODUCTS,
        PERMISSIONS.VIEW_CUSTOMERS
    ]
};

// Global users array
let systemUsers = [];

// User Management Class
class UserManager {
    constructor() {
        this.currentUser = null;
        // Clear any corrupted data first
        this.clearCorruptedData();
        this.loadUsers();
        this.loadCurrentUser();
    }

    // Load users from localStorage
    loadUsers() {
        try {
            const savedUsers = localStorage.getItem('systemUsers');
            if (savedUsers) {
                // Check if it's valid JSON
                if (savedUsers.startsWith('[') && savedUsers.endsWith(']')) {
                    const loadedUsers = JSON.parse(savedUsers);
                    // Validate users array
                    if (!Array.isArray(loadedUsers)) {
                        throw new Error('بيانات المستخدمين ليست مصفوفة صحيحة');
                    }

                    // Clear and update systemUsers array properly
                    systemUsers.length = 0; // Clear existing array
                    systemUsers.push(...loadedUsers); // Add all loaded users

                    console.log('✅ تم تحميل بيانات المستخدمين:', systemUsers.length);
                    console.log('👥 المستخدمون المحملون:');
                    systemUsers.forEach((user, index) => {
                        console.log(`  ${index + 1}. ${user.name} - ${user.email} - نشط: ${user.isActive}`);
                    });
                } else {
                    throw new Error('بيانات المستخدمين تالفة');
                }
            } else {
                // Initialize with default admin user
                console.log('⚠️ لا توجد بيانات مستخدمين محفوظة - إنشاء المدير الافتراضي');
                this.initializeDefaultUsers();
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل المستخدمين:', error);
            console.log('🔄 إعادة تهيئة المستخدمين الافتراضيين...');
            systemUsers.length = 0; // Clear array
            this.initializeDefaultUsers();
        }
    }

    // Initialize default users
    initializeDefaultUsers() {
        systemUsers = [{
            id: this.generateUserId(),
            name: 'المدير الرئيسي',
            email: '<EMAIL>',
            password: '2030',
            role: USER_ROLES.ADMIN,
            permissions: ROLE_PERMISSIONS[USER_ROLES.ADMIN],
            isActive: true,
            createdAt: new Date().toISOString(),
            lastLogin: null
        }];
        this.saveUsers();
        console.log('✅ تم إنشاء المستخدم الافتراضي بنجاح');
    }

    // Save users to localStorage
    saveUsers() {
        try {
            localStorage.setItem('systemUsers', JSON.stringify(systemUsers));
            console.log('✅ تم حفظ بيانات المستخدمين');
            console.log('💾 عدد المستخدمين المحفوظين:', systemUsers.length);

            // Force reload users from localStorage to ensure consistency
            this.loadUsers();
        } catch (error) {
            console.error('❌ خطأ في حفظ المستخدمين:', error);
        }
    }

    // Load current user
    loadCurrentUser() {
        try {
            const currentUserData = localStorage.getItem('currentUser');
            if (currentUserData) {
                // Check if it's valid JSON
                if (currentUserData.startsWith('{') && currentUserData.endsWith('}')) {
                    this.currentUser = JSON.parse(currentUserData);
                } else {
                    // Invalid JSON, clear it
                    console.log('⚠️ بيانات المستخدم الحالي تالفة، سيتم مسحها');
                    localStorage.removeItem('currentUser');
                    this.currentUser = null;
                }
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل المستخدم الحالي:', error);
            // Clear corrupted data
            localStorage.removeItem('currentUser');
            this.currentUser = null;
        }
    }

    // Save current user
    saveCurrentUser() {
        try {
            if (this.currentUser) {
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
            }
        } catch (error) {
            console.error('❌ خطأ في حفظ المستخدم الحالي:', error);
        }
    }

    // Generate unique user ID
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Add new user
    addUser(userData) {
        try {
            // Validate required fields
            if (!userData.name || !userData.email || !userData.password || !userData.role) {
                throw new Error('جميع الحقول مطلوبة');
            }

            // Check if email already exists
            const existingUser = systemUsers.find(user => user.email === userData.email);
            if (existingUser) {
                throw new Error('البريد الإلكتروني مسجل مسبقاً');
            }

            // Create new user
            const newUser = {
                id: this.generateUserId(),
                name: userData.name.trim(),
                email: userData.email.trim().toLowerCase(),
                password: userData.password.trim(), // إضافة trim() لكلمة المرور
                role: userData.role,
                permissions: userData.permissions || ROLE_PERMISSIONS[userData.role] || [],
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null
            };

            systemUsers.push(newUser);
            this.saveUsers();

            console.log('✅ تم إضافة المستخدم بنجاح:', newUser.name);
            console.log('📧 البريد المحفوظ:', `"${newUser.email}"`);
            console.log('🔑 كلمة المرور المحفوظة:', `"${newUser.password}"`);
            console.log('🎭 الدور:', newUser.role);
            console.log('✅ نشط:', newUser.isActive);
            console.log('👥 إجمالي المستخدمين الآن:', systemUsers.length);

            // Verify user was added correctly
            const verifyUser = systemUsers.find(u => u.id === newUser.id);
            if (verifyUser) {
                console.log('✅ تم التحقق من إضافة المستخدم في المصفوفة');
            } else {
                console.error('❌ المستخدم غير موجود في المصفوفة بعد الإضافة!');
            }

            return { success: true, user: newUser };

        } catch (error) {
            console.error('❌ خطأ في إضافة المستخدم:', error);
            return { success: false, error: error.message };
        }
    }

    // Update user
    updateUser(userId, updateData) {
        try {
            const userIndex = systemUsers.findIndex(user => user.id === userId);
            if (userIndex === -1) {
                throw new Error('المستخدم غير موجود');
            }

            // Update user data
            systemUsers[userIndex] = {
                ...systemUsers[userIndex],
                ...updateData,
                updatedAt: new Date().toISOString()
            };

            this.saveUsers();

            console.log('✅ تم تحديث المستخدم بنجاح');
            return { success: true, user: systemUsers[userIndex] };

        } catch (error) {
            console.error('❌ خطأ في تحديث المستخدم:', error);
            return { success: false, error: error.message };
        }
    }

    // Delete user
    deleteUser(userId) {
        try {
            const userIndex = systemUsers.findIndex(user => user.id === userId);
            if (userIndex === -1) {
                throw new Error('المستخدم غير موجود');
            }

            // Don't allow deleting the last admin
            const user = systemUsers[userIndex];
            if (user.role === USER_ROLES.ADMIN) {
                const adminCount = systemUsers.filter(u => u.role === USER_ROLES.ADMIN && u.isActive).length;
                if (adminCount <= 1) {
                    throw new Error('لا يمكن حذف آخر مدير في النظام');
                }
            }

            systemUsers.splice(userIndex, 1);
            this.saveUsers();

            console.log('✅ تم حذف المستخدم بنجاح');
            return { success: true };

        } catch (error) {
            console.error('❌ خطأ في حذف المستخدم:', error);
            return { success: false, error: error.message };
        }
    }

    // Authenticate user
    authenticateUser(email, password) {
        try {
            console.log('🔐 محاولة تسجيل الدخول...');
            console.log('📧 البريد المدخل:', email);
            console.log('🔑 كلمة المرور المدخلة:', password ? '***' : 'فارغة');

            // Force reload users from localStorage before authentication
            console.log('🔄 إعادة تحميل المستخدمين من التخزين المحلي...');
            this.loadUsers();

            console.log('👥 عدد المستخدمين في النظام:', systemUsers.length);

            // Log all users for debugging
            console.log('📋 المستخدمون المتاحون:');
            systemUsers.forEach((u, index) => {
                console.log(`  ${index + 1}. ${u.name} (${u.email}) - نشط: ${u.isActive}`);
            });

            // Validate input
            if (!email || !password) {
                throw new Error('يرجى إدخال البريد الإلكتروني وكلمة المرور');
            }

            const emailLower = email.toLowerCase().trim();
            const passwordTrimmed = password.trim();

            console.log('🔍 البحث عن مستخدم بالبريد:', emailLower);

            const user = systemUsers.find(u => {
                const userEmailMatch = u.email === emailLower;
                const userPasswordMatch = u.password === passwordTrimmed;
                const userActive = u.isActive;

                console.log(`  فحص ${u.name}:`);
                console.log(`    البريد متطابق: ${userEmailMatch} (${u.email} === ${emailLower})`);
                console.log(`    كلمة المرور متطابقة: ${userPasswordMatch}`);
                console.log(`    المستخدم نشط: ${userActive}`);

                return userEmailMatch && userPasswordMatch && userActive;
            });

            if (user) {
                console.log('✅ تم العثور على المستخدم:', user.name);

                // Update last login
                user.lastLogin = new Date().toISOString();
                this.currentUser = user;
                this.saveUsers();
                this.saveCurrentUser();

                console.log('✅ تم تسجيل الدخول بنجاح:', user.name);
                return { success: true, user: user };
            } else {
                console.log('❌ لم يتم العثور على مستخدم مطابق');
                throw new Error('بيانات تسجيل الدخول غير صحيحة');
            }

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            return { success: false, error: error.message };
        }
    }

    // Check if user has permission
    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes(permission);
    }

    // Get all users
    getAllUsers() {
        return systemUsers.filter(user => user.isActive);
    }

    // Get user by ID
    getUserById(userId) {
        return systemUsers.find(user => user.id === userId);
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Logout current user
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        console.log('✅ تم تسجيل الخروج بنجاح');
    }

    // Clear corrupted data
    clearCorruptedData() {
        try {
            console.log('🧹 مسح البيانات التالفة...');

            // Clear corrupted user data
            const corruptedKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('User') || key.includes('user'))) {
                    try {
                        const value = localStorage.getItem(key);
                        if (value && !value.startsWith('{') && !value.startsWith('[')) {
                            corruptedKeys.push(key);
                        } else if (value) {
                            JSON.parse(value); // Test if valid JSON
                        }
                    } catch (error) {
                        corruptedKeys.push(key);
                    }
                }
            }

            // Remove corrupted keys
            corruptedKeys.forEach(key => {
                localStorage.removeItem(key);
                console.log(`🗑️ تم مسح البيانات التالفة: ${key}`);
            });

            if (corruptedKeys.length > 0) {
                console.log(`✅ تم مسح ${corruptedKeys.length} عنصر تالف`);
                // Reinitialize
                this.loadUsers();
                this.loadCurrentUser();
            } else {
                console.log('✅ لا توجد بيانات تالفة');
            }

        } catch (error) {
            console.error('❌ خطأ في مسح البيانات التالفة:', error);
        }
    }
}

// Global user manager instance
const userManager = new UserManager();

// Export for global use
window.userManager = userManager;
window.USER_ROLES = USER_ROLES;
window.PERMISSIONS = PERMISSIONS;
window.ROLE_PERMISSIONS = ROLE_PERMISSIONS;

// UI Functions for User Management
// وظائف واجهة المستخدم لإدارة المستخدمين

/**
 * Show add user modal
 */
function showAddUserModal() {
    const modalHtml = `
        <div class="modal active" id="addUserModal">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h2><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</h2>
                    <span class="close" onclick="closeModal('addUserModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="addUserForm" onsubmit="addNewUser(event)">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="userName">الاسم الكامل *</label>
                                <input type="text" id="userName" name="userName" required placeholder="أدخل الاسم الكامل">
                            </div>
                            <div class="form-group">
                                <label for="userEmail">البريد الإلكتروني *</label>
                                <input type="email" id="userEmail" name="userEmail" required placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="userPassword">كلمة المرور *</label>
                                <input type="password" id="userPassword" name="userPassword" required placeholder="كلمة مرور قوية">
                            </div>
                            <div class="form-group">
                                <label for="userRole">الدور *</label>
                                <select id="userRole" name="userRole" required onchange="updatePermissionsPreview()">
                                    <option value="">اختر الدور</option>
                                    <option value="admin">مدير (صلاحيات كاملة)</option>
                                    <option value="manager">مدير قسم (صلاحيات محدودة)</option>
                                    <option value="employee">موظف (إضافة وعرض)</option>
                                    <option value="viewer">مشاهد (عرض فقط)</option>
                                </select>
                            </div>
                        </div>

                        <div class="permissions-preview" id="permissionsPreview" style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 8px; display: none;">
                            <h4><i class="fas fa-key"></i> الصلاحيات المتاحة:</h4>
                            <div id="permissionsList"></div>
                        </div>

                        <div class="custom-permissions" style="margin: 20px 0;">
                            <label>
                                <input type="checkbox" id="customPermissions" onchange="toggleCustomPermissions()">
                                تخصيص الصلاحيات يدوياً
                            </label>
                            <div id="customPermissionsContainer" style="display: none; margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">
                                <div class="permissions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                                    <label><input type="checkbox" name="permissions" value="view_dashboard"> عرض لوحة التحكم</label>
                                    <label><input type="checkbox" name="permissions" value="view_products"> عرض المنتجات</label>
                                    <label><input type="checkbox" name="permissions" value="add_products"> إضافة منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="edit_products"> تعديل منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="delete_products"> حذف منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="view_customers"> عرض العملاء</label>
                                    <label><input type="checkbox" name="permissions" value="add_customers"> إضافة عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="edit_customers"> تعديل عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="delete_customers"> حذف عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="view_settings"> عرض الإعدادات</label>
                                    <label><input type="checkbox" name="permissions" value="edit_settings"> تعديل الإعدادات</label>
                                    <label><input type="checkbox" name="permissions" value="manage_users"> إدارة المستخدمين</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> إضافة المستخدم
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addUserModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

/**
 * Update permissions preview based on selected role
 */
function updatePermissionsPreview() {
    const roleSelect = document.getElementById('userRole');
    const previewDiv = document.getElementById('permissionsPreview');
    const permissionsList = document.getElementById('permissionsList');

    if (roleSelect.value) {
        const permissions = ROLE_PERMISSIONS[roleSelect.value] || [];
        const permissionNames = {
            'view_dashboard': 'عرض لوحة التحكم',
            'view_products': 'عرض المنتجات',
            'add_products': 'إضافة منتجات',
            'edit_products': 'تعديل منتجات',
            'delete_products': 'حذف منتجات',
            'view_customers': 'عرض العملاء',
            'add_customers': 'إضافة عملاء',
            'edit_customers': 'تعديل عملاء',
            'delete_customers': 'حذف عملاء',
            'view_settings': 'عرض الإعدادات',
            'edit_settings': 'تعديل الإعدادات',
            'manage_users': 'إدارة المستخدمين'
        };

        const permissionsHtml = permissions.map(permission =>
            `<span class="permission-badge" style="display: inline-block; background: #e3f2fd; color: #1976d2; padding: 4px 8px; margin: 2px; border-radius: 4px; font-size: 12px;">
                <i class="fas fa-check"></i> ${permissionNames[permission] || permission}
            </span>`
        ).join('');

        permissionsList.innerHTML = permissionsHtml;
        previewDiv.style.display = 'block';
    } else {
        previewDiv.style.display = 'none';
    }
}

/**
 * Toggle custom permissions section
 */
function toggleCustomPermissions() {
    const checkbox = document.getElementById('customPermissions');
    const container = document.getElementById('customPermissionsContainer');
    const roleSelect = document.getElementById('userRole');

    if (checkbox.checked) {
        container.style.display = 'block';
        roleSelect.disabled = true;

        // Pre-select permissions based on current role
        if (roleSelect.value) {
            const permissions = ROLE_PERMISSIONS[roleSelect.value] || [];
            const checkboxes = container.querySelectorAll('input[name="permissions"]');
            checkboxes.forEach(cb => {
                cb.checked = permissions.includes(cb.value);
            });
        }
    } else {
        container.style.display = 'none';
        roleSelect.disabled = false;
    }
}

/**
 * Add new user
 */
function addNewUser(event) {
    event.preventDefault();

    console.log('🔄 بدء إضافة مستخدم جديد...');

    try {
        const formData = new FormData(event.target);

        // Log form data for debugging
        console.log('📝 بيانات النموذج:');
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }

        const customPermissionsEnabled = document.getElementById('customPermissions')?.checked || false;
        console.log('🔧 الصلاحيات المخصصة:', customPermissionsEnabled);

        let permissions;
        if (customPermissionsEnabled) {
            // Get custom permissions
            const selectedPermissions = Array.from(document.querySelectorAll('input[name="permissions"]:checked'))
                .map(cb => cb.value);
            permissions = selectedPermissions;
            console.log('🔐 الصلاحيات المحددة:', permissions);
        } else {
            // Use role-based permissions
            const userRole = formData.get('userRole');
            permissions = ROLE_PERMISSIONS[userRole] || [];
            console.log('👤 صلاحيات الدور', userRole, ':', permissions);
        }

        const userData = {
            name: formData.get('userName'),
            email: formData.get('userEmail'),
            password: formData.get('userPassword'),
            role: formData.get('userRole'),
            permissions: permissions
        };

        console.log('👤 بيانات المستخدم النهائية:', userData);

        // Validate required fields
        if (!userData.name || !userData.email || !userData.password || !userData.role) {
            const missingFields = [];
            if (!userData.name) missingFields.push('الاسم');
            if (!userData.email) missingFields.push('البريد الإلكتروني');
            if (!userData.password) missingFields.push('كلمة المرور');
            if (!userData.role) missingFields.push('الدور');

            const errorMsg = `الحقول التالية مطلوبة: ${missingFields.join('، ')}`;
            console.error('❌', errorMsg);

            if (typeof showToast === 'function') {
                showToast(errorMsg, 'error');
            } else {
                alert(errorMsg);
            }
            return;
        }

        const result = userManager.addUser(userData);
        console.log('📊 نتيجة إضافة المستخدم:', result);

        if (result.success) {
            console.log('✅ تم إضافة المستخدم بنجاح');

            // Test login immediately after adding user
            console.log('🧪 اختبار تسجيل الدخول للمستخدم الجديد...');
            setTimeout(() => {
                console.log('📋 بيانات المستخدم المُدخلة:');
                console.log('  الاسم:', `"${userData.name}"`);
                console.log('  البريد:', `"${userData.email}"`);
                console.log('  كلمة المرور:', `"${userData.password}"`);
                console.log('  الدور:', userData.role);

                // Check if user exists in systemUsers array
                const allUsers = userManager.getAllUsers();
                console.log('👥 جميع المستخدمين بعد الإضافة:');
                allUsers.forEach((u, index) => {
                    console.log(`  ${index + 1}. ${u.name} - ${u.email} - كلمة المرور: "${u.password}" - نشط: ${u.isActive}`);
                });

                // Find the newly added user
                const newlyAddedUser = allUsers.find(u => u.email === userData.email.trim().toLowerCase());
                if (newlyAddedUser) {
                    console.log('✅ تم العثور على المستخدم الجديد في القائمة:');
                    console.log('  الاسم:', newlyAddedUser.name);
                    console.log('  البريد:', `"${newlyAddedUser.email}"`);
                    console.log('  كلمة المرور المحفوظة:', `"${newlyAddedUser.password}"`);
                    console.log('  نشط:', newlyAddedUser.isActive);
                } else {
                    console.error('❌ لم يتم العثور على المستخدم الجديد في القائمة!');
                }

                const testLoginResult = userManager.authenticateUser(userData.email, userData.password);
                if (testLoginResult.success) {
                    console.log('✅ اختبار تسجيل الدخول نجح للمستخدم الجديد');
                } else {
                    console.error('❌ اختبار تسجيل الدخول فشل للمستخدم الجديد:', testLoginResult.error);

                    // Try with exact saved data
                    if (newlyAddedUser) {
                        console.log('🔄 محاولة تسجيل الدخول بالبيانات المحفوظة...');
                        const testWithSavedData = userManager.authenticateUser(newlyAddedUser.email, newlyAddedUser.password);
                        if (testWithSavedData.success) {
                            console.log('✅ نجح تسجيل الدخول بالبيانات المحفوظة');
                        } else {
                            console.error('❌ فشل حتى مع البيانات المحفوظة:', testWithSavedData.error);
                        }
                    }
                }
            }, 1000);

            // Close modal
            const modal = document.getElementById('addUserModal');
            if (modal) {
                modal.remove();
                console.log('🗑️ تم إغلاق النافذة المنبثقة');
            }

            // Reload users table
            loadUsersTable();
            console.log('🔄 تم تحديث جدول المستخدمين');

            // Show success message
            const successMsg = `تم إضافة المستخدم "${userData.name}" بنجاح`;
            if (typeof showToast === 'function') {
                showToast(successMsg, 'success');
            } else {
                alert(successMsg);
            }

        } else {
            console.error('❌ فشل في إضافة المستخدم:', result.error);

            const errorMsg = 'خطأ في إضافة المستخدم: ' + result.error;
            if (typeof showToast === 'function') {
                showToast(errorMsg, 'error');
            } else {
                alert(errorMsg);
            }
        }

    } catch (error) {
        console.error('❌ خطأ غير متوقع في إضافة المستخدم:', error);

        const errorMsg = 'حدث خطأ غير متوقع: ' + error.message;
        if (typeof showToast === 'function') {
            showToast(errorMsg, 'error');
        } else {
            alert(errorMsg);
        }
    }
}

/**
 * Load users table
 */
function loadUsersTable() {
    console.log('🔄 تحديث جدول المستخدمين...');

    const tableBody = document.getElementById('usersTableBody');
    if (!tableBody) {
        console.log('⚠️ عنصر جدول المستخدمين غير موجود');
        return;
    }

    const users = userManager.getAllUsers();
    console.log(`👥 عدد المستخدمين: ${users.length}`);

    if (users.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                    <i class="fas fa-users"></i><br>
                    لا يوجد مستخدمون مسجلون
                </td>
            </tr>
        `;
        return;
    }

    const roleNames = {
        'admin': 'مدير',
        'manager': 'مدير قسم',
        'employee': 'موظف',
        'viewer': 'مشاهد'
    };

    tableBody.innerHTML = users.map(user => {
        const permissionsCount = user.permissions.length;
        const lastLogin = user.lastLogin ?
            new Date(user.lastLogin).toLocaleDateString('ar-SA') : 'لم يسجل دخول';

        return `
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">${user.name}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">${user.email}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <span class="role-badge role-${user.role}" style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        ${roleNames[user.role] || user.role}
                    </span>
                </td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <span class="permissions-count" style="background: #f3e5f5; color: #7b1fa2; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        ${permissionsCount} صلاحية
                    </span>
                </td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <span class="status-badge ${user.isActive ? 'status-active' : 'status-inactive'}"
                          style="background: ${user.isActive ? '#e8f5e8' : '#ffebee'};
                                 color: ${user.isActive ? '#2e7d32' : '#c62828'};
                                 padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        <i class="fas ${user.isActive ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                        ${user.isActive ? 'نشط' : 'معطل'}
                    </span>
                    <br><small style="color: #666;">آخر دخول: ${lastLogin}</small>
                </td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <div class="action-buttons" style="display: flex; gap: 5px;">
                        <button class="btn btn-sm btn-info" onclick="viewUserDetails('${user.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editUser('${user.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${user.role !== 'admin' ? `
                            <button class="btn btn-sm btn-danger" onclick="deleteUser('${user.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * View user details
 */
function viewUserDetails(userId) {
    const user = userManager.getUserById(userId);
    if (!user) return;

    const permissionNames = {
        'view_dashboard': 'عرض لوحة التحكم',
        'view_products': 'عرض المنتجات',
        'add_products': 'إضافة منتجات',
        'edit_products': 'تعديل منتجات',
        'delete_products': 'حذف منتجات',
        'view_customers': 'عرض العملاء',
        'add_customers': 'إضافة عملاء',
        'edit_customers': 'تعديل عملاء',
        'delete_customers': 'حذف عملاء',
        'view_settings': 'عرض الإعدادات',
        'edit_settings': 'تعديل الإعدادات',
        'manage_users': 'إدارة المستخدمين'
    };

    const permissionsHtml = user.permissions.map(permission =>
        `<li style="margin: 5px 0;"><i class="fas fa-check" style="color: #4caf50;"></i> ${permissionNames[permission] || permission}</li>`
    ).join('');

    const modalHtml = `
        <div class="modal active" id="userDetailsModal">
            <div class="modal-content medium-modal">
                <div class="modal-header">
                    <h2><i class="fas fa-user"></i> تفاصيل المستخدم</h2>
                    <span class="close" onclick="closeModal('userDetailsModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="user-details">
                        <div class="detail-item">
                            <strong>الاسم:</strong> ${user.name}
                        </div>
                        <div class="detail-item">
                            <strong>البريد الإلكتروني:</strong> ${user.email}
                        </div>
                        <div class="detail-item">
                            <strong>الدور:</strong> ${user.role}
                        </div>
                        <div class="detail-item">
                            <strong>تاريخ الإنشاء:</strong> ${new Date(user.createdAt).toLocaleDateString('ar-SA')}
                        </div>
                        <div class="detail-item">
                            <strong>آخر تسجيل دخول:</strong> ${user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar-SA') : 'لم يسجل دخول'}
                        </div>
                        <div class="detail-item">
                            <strong>الحالة:</strong>
                            <span style="color: ${user.isActive ? '#4caf50' : '#f44336'};">
                                ${user.isActive ? 'نشط' : 'معطل'}
                            </span>
                        </div>
                        <div class="detail-item">
                            <strong>الصلاحيات:</strong>
                            <ul style="margin: 10px 0; padding-right: 20px;">
                                ${permissionsHtml}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal('userDetailsModal')">إغلاق</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

/**
 * Edit user
 */
function editUser(userId) {
    const user = userManager.getUserById(userId);
    if (!user) return;

    const modalHtml = `
        <div class="modal active" id="editUserModal">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h2><i class="fas fa-user-edit"></i> تعديل المستخدم</h2>
                    <span class="close" onclick="closeModal('editUserModal')">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="editUserForm" onsubmit="updateUser(event, '${userId}')">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="editUserName">الاسم الكامل *</label>
                                <input type="text" id="editUserName" name="userName" value="${user.name}" required>
                            </div>
                            <div class="form-group">
                                <label for="editUserEmail">البريد الإلكتروني *</label>
                                <input type="email" id="editUserEmail" name="userEmail" value="${user.email}" required>
                            </div>
                            <div class="form-group">
                                <label for="editUserPassword">كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية)</label>
                                <input type="password" id="editUserPassword" name="userPassword" placeholder="كلمة مرور جديدة">
                            </div>
                            <div class="form-group">
                                <label for="editUserRole">الدور *</label>
                                <select id="editUserRole" name="userRole" required>
                                    <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>مدير (صلاحيات كاملة)</option>
                                    <option value="manager" ${user.role === 'manager' ? 'selected' : ''}>مدير قسم (صلاحيات محدودة)</option>
                                    <option value="employee" ${user.role === 'employee' ? 'selected' : ''}>موظف (إضافة وعرض)</option>
                                    <option value="viewer" ${user.role === 'viewer' ? 'selected' : ''}>مشاهد (عرض فقط)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="editUserStatus">الحالة</label>
                                <select id="editUserStatus" name="userStatus">
                                    <option value="true" ${user.isActive ? 'selected' : ''}>نشط</option>
                                    <option value="false" ${!user.isActive ? 'selected' : ''}>معطل</option>
                                </select>
                            </div>
                        </div>

                        <div class="custom-permissions" style="margin: 20px 0;">
                            <label>
                                <input type="checkbox" id="editCustomPermissions" onchange="toggleEditCustomPermissions()">
                                تخصيص الصلاحيات يدوياً
                            </label>
                            <div id="editCustomPermissionsContainer" style="display: none; margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">
                                <div class="permissions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;">
                                    <label><input type="checkbox" name="permissions" value="view_dashboard" ${user.permissions.includes('view_dashboard') ? 'checked' : ''}> عرض لوحة التحكم</label>
                                    <label><input type="checkbox" name="permissions" value="view_products" ${user.permissions.includes('view_products') ? 'checked' : ''}> عرض المنتجات</label>
                                    <label><input type="checkbox" name="permissions" value="add_products" ${user.permissions.includes('add_products') ? 'checked' : ''}> إضافة منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="edit_products" ${user.permissions.includes('edit_products') ? 'checked' : ''}> تعديل منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="delete_products" ${user.permissions.includes('delete_products') ? 'checked' : ''}> حذف منتجات</label>
                                    <label><input type="checkbox" name="permissions" value="view_customers" ${user.permissions.includes('view_customers') ? 'checked' : ''}> عرض العملاء</label>
                                    <label><input type="checkbox" name="permissions" value="add_customers" ${user.permissions.includes('add_customers') ? 'checked' : ''}> إضافة عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="edit_customers" ${user.permissions.includes('edit_customers') ? 'checked' : ''}> تعديل عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="delete_customers" ${user.permissions.includes('delete_customers') ? 'checked' : ''}> حذف عملاء</label>
                                    <label><input type="checkbox" name="permissions" value="view_settings" ${user.permissions.includes('view_settings') ? 'checked' : ''}> عرض الإعدادات</label>
                                    <label><input type="checkbox" name="permissions" value="edit_settings" ${user.permissions.includes('edit_settings') ? 'checked' : ''}> تعديل الإعدادات</label>
                                    <label><input type="checkbox" name="permissions" value="manage_users" ${user.permissions.includes('manage_users') ? 'checked' : ''}> إدارة المستخدمين</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="closeModal('editUserModal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

/**
 * Toggle custom permissions in edit modal
 */
function toggleEditCustomPermissions() {
    const checkbox = document.getElementById('editCustomPermissions');
    const container = document.getElementById('editCustomPermissionsContainer');
    const roleSelect = document.getElementById('editUserRole');

    if (checkbox.checked) {
        container.style.display = 'block';
        roleSelect.disabled = true;
    } else {
        container.style.display = 'none';
        roleSelect.disabled = false;
    }
}

/**
 * Update user
 */
function updateUser(event, userId) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const customPermissionsEnabled = document.getElementById('editCustomPermissions').checked;

    let updateData = {
        name: formData.get('userName'),
        email: formData.get('userEmail'),
        role: formData.get('userRole'),
        isActive: formData.get('userStatus') === 'true'
    };

    // Update password only if provided
    const newPassword = formData.get('userPassword');
    if (newPassword && newPassword.trim()) {
        updateData.password = newPassword.trim();
    }

    // Update permissions
    if (customPermissionsEnabled) {
        const selectedPermissions = Array.from(document.querySelectorAll('#editCustomPermissionsContainer input[name="permissions"]:checked'))
            .map(cb => cb.value);
        updateData.permissions = selectedPermissions;
    } else {
        updateData.permissions = ROLE_PERMISSIONS[updateData.role] || [];
    }

    const result = userManager.updateUser(userId, updateData);

    if (result.success) {
        closeModal('editUserModal');
        loadUsersTable();
        if (typeof showToast === 'function') {
            showToast('تم تحديث المستخدم بنجاح', 'success');
        }
    } else {
        if (typeof showToast === 'function') {
            showToast('خطأ: ' + result.error, 'error');
        }
    }
}

/**
 * Delete user
 */
function deleteUser(userId) {
    const user = userManager.getUserById(userId);
    if (!user) return;

    if (confirm(`هل أنت متأكد من حذف المستخدم "${user.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        const result = userManager.deleteUser(userId);

        if (result.success) {
            loadUsersTable();
            if (typeof showToast === 'function') {
                showToast('تم حذف المستخدم بنجاح', 'success');
            }
        } else {
            if (typeof showToast === 'function') {
                showToast('خطأ: ' + result.error, 'error');
            }
        }
    }
}

/**
 * Fix corrupted data
 */
function fixCorruptedData() {
    if (typeof userManager !== 'undefined') {
        userManager.clearCorruptedData();
        loadUsersTable();
        if (typeof showToast === 'function') {
            showToast('تم إصلاح البيانات التالفة', 'success');
        }
    }
}

/**
 * Reset user management system
 */
function resetUserManagement() {
    if (confirm('هل أنت متأكد من إعادة تعيين نظام إدارة المستخدمين؟\nسيتم حذف جميع المستخدمين عدا المدير الافتراضي.')) {
        // Clear all user data
        localStorage.removeItem('systemUsers');
        localStorage.removeItem('currentUser');

        // Reinitialize
        if (typeof userManager !== 'undefined') {
            userManager.initializeDefaultUsers();
            userManager.currentUser = null;
            loadUsersTable();
        }

        if (typeof showToast === 'function') {
            showToast('تم إعادة تعيين نظام إدارة المستخدمين', 'success');
        }
    }
}

/**
 * Test add user function
 */
function testAddUser() {
    console.log('🧪 اختبار إضافة مستخدم...');

    const timestamp = Date.now();
    const testUserData = {
        name: 'مستخدم تجريبي ' + timestamp,
        email: 'test' + timestamp + '@example.com',
        password: 'test123',
        role: 'employee',
        permissions: ROLE_PERMISSIONS['employee']
    };

    console.log('👤 بيانات المستخدم التجريبي:', testUserData);
    console.log('📧 البريد:', `"${testUserData.email}"`);
    console.log('🔑 كلمة المرور:', `"${testUserData.password}"`);

    const result = userManager.addUser(testUserData);
    console.log('📊 نتيجة الإضافة:', result);

    if (result.success) {
        console.log('✅ تم إضافة المستخدم التجريبي بنجاح');

        // Test login immediately
        setTimeout(() => {
            console.log('🧪 اختبار تسجيل الدخول للمستخدم التجريبي...');
            const loginResult = userManager.authenticateUser(testUserData.email, testUserData.password);

            if (loginResult.success) {
                console.log('✅ نجح تسجيل الدخول للمستخدم التجريبي');
                showToast(`تم إضافة واختبار المستخدم "${testUserData.name}" بنجاح`, 'success');
            } else {
                console.error('❌ فشل تسجيل الدخول للمستخدم التجريبي:', loginResult.error);
                showToast(`تم إضافة المستخدم ولكن فشل تسجيل الدخول: ${loginResult.error}`, 'warning');

                // Show detailed comparison
                const allUsers = userManager.getAllUsers();
                const addedUser = allUsers.find(u => u.email === testUserData.email.toLowerCase());
                if (addedUser) {
                    console.log('🔍 مقارنة البيانات:');
                    console.log('  البريد المُدخل:', `"${testUserData.email}"`);
                    console.log('  البريد المحفوظ:', `"${addedUser.email}"`);
                    console.log('  كلمة المرور المُدخلة:', `"${testUserData.password}"`);
                    console.log('  كلمة المرور المحفوظة:', `"${addedUser.password}"`);
                    console.log('  المستخدم نشط:', addedUser.isActive);
                }
            }
        }, 1000);

        loadUsersTable();
    } else {
        showToast('فشل في إضافة المستخدم التجريبي: ' + result.error, 'error');
        console.error('❌ فشل في إضافة المستخدم التجريبي:', result.error);
    }
}

/**
 * Debug users and passwords
 */
function debugUsersAndPasswords() {
    console.log('🔍 تشخيص المستخدمين وكلمات المرور...');

    const users = userManager.getAllUsers();
    console.log(`👥 عدد المستخدمين: ${users.length}`);

    console.log('📋 تفاصيل جميع المستخدمين:');
    users.forEach((user, index) => {
        console.log(`\n${index + 1}. المستخدم: ${user.name}`);
        console.log(`   📧 البريد: "${user.email}"`);
        console.log(`   🔑 كلمة المرور: "${user.password}"`);
        console.log(`   🎭 الدور: ${user.role}`);
        console.log(`   ✅ نشط: ${user.isActive}`);
        console.log(`   📅 تاريخ الإنشاء: ${user.createdAt}`);
        console.log(`   🕐 آخر دخول: ${user.lastLogin || 'لم يسجل دخول'}`);
    });

    // Test login with default admin
    console.log('\n🧪 اختبار تسجيل الدخول للمدير الافتراضي...');
    const testResult = userManager.authenticateUser('<EMAIL>', '2030');
    console.log('📊 نتيجة الاختبار:', testResult);
}

/**
 * Reset admin password
 */
function resetAdminPassword() {
    console.log('🔄 إعادة تعيين كلمة مرور المدير...');

    const users = userManager.getAllUsers();
    const admin = users.find(u => u.email === '<EMAIL>');

    if (admin) {
        const updateResult = userManager.updateUser(admin.id, {
            password: '2030',
            name: 'المدير الرئيسي',
            role: USER_ROLES.ADMIN,
            permissions: ROLE_PERMISSIONS[USER_ROLES.ADMIN],
            isActive: true
        });

        if (updateResult.success) {
            console.log('✅ تم إعادة تعيين كلمة مرور المدير بنجاح');
            showToast('تم إعادة تعيين كلمة مرور المدير إلى: 2030', 'success');
            loadUsersTable();
        } else {
            console.error('❌ فشل في إعادة تعيين كلمة المرور:', updateResult.error);
            showToast('فشل في إعادة تعيين كلمة المرور', 'error');
        }
    } else {
        console.log('⚠️ المدير الافتراضي غير موجود - إنشاء جديد...');

        const adminData = {
            name: 'المدير الرئيسي',
            email: '<EMAIL>',
            password: '2030',
            role: USER_ROLES.ADMIN,
            permissions: ROLE_PERMISSIONS[USER_ROLES.ADMIN]
        };

        const createResult = userManager.addUser(adminData);
        if (createResult.success) {
            console.log('✅ تم إنشاء المدير الافتراضي بنجاح');
            showToast('تم إنشاء المدير الافتراضي بنجاح', 'success');
            loadUsersTable();
        } else {
            console.error('❌ فشل في إنشاء المدير:', createResult.error);
            showToast('فشل في إنشاء المدير الافتراضي', 'error');
        }
    }
}

/**
 * Fix all user passwords (remove extra spaces)
 */
function fixAllUserPasswords() {
    console.log('🔧 إصلاح كلمات مرور جميع المستخدمين...');

    const users = userManager.getAllUsers();
    let fixedCount = 0;

    users.forEach(user => {
        const originalPassword = user.password;
        const trimmedPassword = originalPassword.trim();

        if (originalPassword !== trimmedPassword) {
            console.log(`🔧 إصلاح كلمة مرور ${user.name}:`);
            console.log(`  قبل: "${originalPassword}"`);
            console.log(`  بعد: "${trimmedPassword}"`);

            const updateResult = userManager.updateUser(user.id, {
                password: trimmedPassword
            });

            if (updateResult.success) {
                fixedCount++;
            }
        }
    });

    if (fixedCount > 0) {
        console.log(`✅ تم إصلاح كلمات مرور ${fixedCount} مستخدم`);
        showToast(`تم إصلاح كلمات مرور ${fixedCount} مستخدم`, 'success');
        loadUsersTable();
    } else {
        console.log('ℹ️ جميع كلمات المرور صحيحة');
        showToast('جميع كلمات المرور صحيحة', 'info');
    }
}

/**
 * Close modal function (if not defined elsewhere)
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.remove();
        console.log(`🗑️ تم إغلاق النافذة: ${modalId}`);
    }
}

/**
 * Show toast notification (if not defined elsewhere)
 */
function showToast(message, type = 'info') {
    console.log(`📢 ${type.toUpperCase()}: ${message}`);

    // Try to use existing toast function
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
        return;
    }

    // Create simple toast if no existing function
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 10001;
        font-family: Arial, sans-serif;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    `;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

/**
 * Test login function
 */
function testLogin() {
    console.log('🧪 اختبار تسجيل الدخول...');

    const email = prompt('أدخل البريد الإلكتروني:', '<EMAIL>');
    if (!email) return;

    const password = prompt('أدخل كلمة المرور:', '2030');
    if (!password) return;

    console.log('🔐 محاولة تسجيل الدخول بالبيانات المدخلة...');
    const result = userManager.authenticateUser(email, password);

    if (result.success) {
        showToast(`تم تسجيل الدخول بنجاح كـ ${result.user.name}`, 'success');
        console.log('✅ نجح تسجيل الدخول');
    } else {
        showToast(`فشل تسجيل الدخول: ${result.error}`, 'error');
        console.error('❌ فشل تسجيل الدخول');
    }
}

/**
 * Show all users with passwords (for debugging)
 */
function showAllUsersWithPasswords() {
    const users = userManager.getAllUsers();

    let usersList = 'المستخدمون المتاحون:\n\n';
    users.forEach((user, index) => {
        usersList += `${index + 1}. ${user.name}\n`;
        usersList += `   البريد: ${user.email}\n`;
        usersList += `   كلمة المرور: ${user.password}\n`;
        usersList += `   الدور: ${user.role}\n`;
        usersList += `   نشط: ${user.isActive ? 'نعم' : 'لا'}\n\n`;
    });

    alert(usersList);
    console.log('👥 جميع المستخدمين:', users);
}

/**
 * Check localStorage directly
 */
function checkLocalStorage() {
    console.log('💾 فحص localStorage مباشرة...');

    const systemUsersData = localStorage.getItem('systemUsers');
    const currentUserData = localStorage.getItem('currentUser');

    console.log('📦 بيانات systemUsers في localStorage:');
    if (systemUsersData) {
        try {
            const parsedUsers = JSON.parse(systemUsersData);
            console.log('✅ تم تحليل البيانات بنجاح:', parsedUsers);
            parsedUsers.forEach((user, index) => {
                console.log(`  ${index + 1}. ${user.name} - ${user.email} - "${user.password}"`);
            });
        } catch (error) {
            console.error('❌ خطأ في تحليل بيانات المستخدمين:', error);
            console.log('📄 البيانات الخام:', systemUsersData);
        }
    } else {
        console.log('⚠️ لا توجد بيانات systemUsers في localStorage');
    }

    console.log('👤 بيانات currentUser في localStorage:');
    if (currentUserData) {
        try {
            const parsedCurrentUser = JSON.parse(currentUserData);
            console.log('✅ المستخدم الحالي:', parsedCurrentUser);
        } catch (error) {
            console.error('❌ خطأ في تحليل بيانات المستخدم الحالي:', error);
            console.log('📄 البيانات الخام:', currentUserData);
        }
    } else {
        console.log('⚠️ لا يوجد مستخدم حالي في localStorage');
    }

    // Show all localStorage keys related to users
    console.log('🔑 جميع مفاتيح localStorage المتعلقة بالمستخدمين:');
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('user') || key.includes('User'))) {
            const value = localStorage.getItem(key);
            console.log(`  ${key}: ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}`);
        }
    }
}

/**
 * Create simple test user
 */
function createSimpleTestUser() {
    console.log('🧪 إنشاء مستخدم تجريبي بسيط...');

    const timestamp = Date.now();
    const simpleUser = {
        name: 'أحمد تجريبي ' + timestamp,
        email: 'ahmed' + timestamp + '@test.com',
        password: '123',
        role: 'employee'
    };

    console.log('👤 بيانات المستخدم البسيط:', simpleUser);

    // Use the proper addUser method
    const result = userManager.addUser(simpleUser);

    if (result.success) {
        console.log('✅ تم إضافة المستخدم البسيط بنجاح');

        // Test login immediately
        setTimeout(() => {
            console.log('🧪 اختبار تسجيل الدخول للمستخدم البسيط...');
            console.log('📧 البريد للاختبار:', simpleUser.email);
            console.log('🔑 كلمة المرور للاختبار:', simpleUser.password);

            const loginResult = userManager.authenticateUser(simpleUser.email, simpleUser.password);

            if (loginResult.success) {
                console.log('✅ نجح تسجيل الدخول للمستخدم البسيط');
                showToast(`تم إنشاء واختبار المستخدم "${simpleUser.name}" بنجاح`, 'success');
            } else {
                console.error('❌ فشل تسجيل الدخول للمستخدم البسيط:', loginResult.error);
                showToast(`فشل في تسجيل الدخول للمستخدم البسيط: ${loginResult.error}`, 'error');

                // Show detailed debugging info
                console.log('🔍 تشخيص مفصل:');
                const allUsers = userManager.getAllUsers();
                const foundUser = allUsers.find(u => u.email === simpleUser.email);
                if (foundUser) {
                    console.log('✅ المستخدم موجود في القائمة:');
                    console.log('  الاسم:', foundUser.name);
                    console.log('  البريد:', `"${foundUser.email}"`);
                    console.log('  كلمة المرور:', `"${foundUser.password}"`);
                    console.log('  نشط:', foundUser.isActive);
                } else {
                    console.error('❌ المستخدم غير موجود في القائمة!');
                }
            }

            loadUsersTable();
        }, 1000);

    } else {
        console.error('❌ فشل في إضافة المستخدم البسيط:', result.error);
        showToast(`فشل في إضافة المستخدم البسيط: ${result.error}`, 'error');
    }
}

/**
 * Initialize users table on page load
 */
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        try {
            loadUsersTable();
        } catch (error) {
            console.error('❌ خطأ في تحميل جدول المستخدمين:', error);
            // Try to fix corrupted data
            fixCorruptedData();
        }
    }, 1000);
});

console.log('✅ تم تحميل نظام إدارة المستخدمين بنجاح');
