package com.diamondeagles.inventory;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.diamondeagles.inventory.databinding.ActivityMainBinding;

/**
 * النشاط الرئيسي لتطبيق النسور الماسية
 * Main Activity for Diamond Eagles Inventory Management System
 */
public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding;
    private WebView webView;
    private SwipeRefreshLayout swipeRefreshLayout;
    private boolean canGoBack = false;
    private FirebaseManager firebaseManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        initializeViews();
        setupWebView();
        initializeFirebase();
        loadApplication();
    }

    private void initializeViews() {
        webView = binding.webView;
        swipeRefreshLayout = binding.swipeRefreshLayout;
        
        // Setup swipe refresh
        swipeRefreshLayout.setOnRefreshListener(() -> {
            webView.reload();
            swipeRefreshLayout.setRefreshing(false);
        });
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        
        // Enable JavaScript
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);
        webSettings.setAppCacheEnabled(true);
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        
        // Enable file access
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setAllowFileAccessFromFileURLs(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);
        
        // Responsive design
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);
        
        // Add JavaScript interface
        webView.addJavascriptInterface(new AndroidInterface(), "Android");
        
        // Set WebView clients
        webView.setWebViewClient(new CustomWebViewClient());
        webView.setWebChromeClient(new WebChromeClient());
    }

    private void initializeFirebase() {
        firebaseManager = FirebaseManager.getInstance(this);

        // تسجيل دخول تلقائي
        firebaseManager.signInAnonymously(new FirebaseManager.OnCompleteListener() {
            @Override
            public void onSuccess(String message) {
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "متصل بالسحابة", Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "وضع عدم الاتصال", Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    private void loadApplication() {
        // Load the main HTML file
        webView.loadUrl("file:///android_asset/index.html");
    }

    /**
     * JavaScript Interface for Android-Web communication
     */
    public class AndroidInterface {
        
        @JavascriptInterface
        public void showToast(String message) {
            runOnUiThread(() -> 
                Toast.makeText(MainActivity.this, message, Toast.LENGTH_SHORT).show()
            );
        }
        
        @JavascriptInterface
        public void exitApp() {
            runOnUiThread(() -> showExitDialog());
        }
        
        @JavascriptInterface
        public void shareText(String text) {
            runOnUiThread(() -> {
                Intent shareIntent = new Intent(Intent.ACTION_SEND);
                shareIntent.setType("text/plain");
                shareIntent.putExtra(Intent.EXTRA_TEXT, text);
                startActivity(Intent.createChooser(shareIntent, "مشاركة"));
            });
        }
        
        @JavascriptInterface
        public String getDeviceInfo() {
            return android.os.Build.MODEL + " - " + android.os.Build.VERSION.RELEASE;
        }

        @JavascriptInterface
        public void uploadProducts(String productsJson) {
            runOnUiThread(() -> {
                if (isNetworkAvailable()) {
                    firebaseManager.uploadProducts(productsJson, new FirebaseManager.OnCompleteListener() {
                        @Override
                        public void onSuccess(String message) {
                            runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, message, Toast.LENGTH_SHORT).show();
                                webView.evaluateJavascript("if(window.onCloudUploadSuccess) window.onCloudUploadSuccess();", null);
                            });
                        }

                        @Override
                        public void onError(String error) {
                            runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, error, Toast.LENGTH_SHORT).show();
                                webView.evaluateJavascript("if(window.onCloudUploadError) window.onCloudUploadError('" + error + "');", null);
                            });
                        }
                    });
                } else {
                    Toast.makeText(MainActivity.this, "لا يوجد اتصال بالإنترنت", Toast.LENGTH_SHORT).show();
                }
            });
        }

        @JavascriptInterface
        public void downloadProducts() {
            runOnUiThread(() -> {
                if (isNetworkAvailable()) {
                    firebaseManager.downloadProducts(new FirebaseManager.OnDataListener() {
                        @Override
                        public void onDataReceived(String data) {
                            runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, "تم تحميل المنتجات", Toast.LENGTH_SHORT).show();
                                webView.evaluateJavascript("if(window.onCloudDownloadSuccess) window.onCloudDownloadSuccess('" + data + "');", null);
                            });
                        }

                        @Override
                        public void onError(String error) {
                            runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, error, Toast.LENGTH_SHORT).show();
                                webView.evaluateJavascript("if(window.onCloudDownloadError) window.onCloudDownloadError('" + error + "');", null);
                            });
                        }
                    });
                } else {
                    Toast.makeText(MainActivity.this, "لا يوجد اتصال بالإنترنت", Toast.LENGTH_SHORT).show();
                }
            });
        }

        @JavascriptInterface
        public void uploadCustomers(String customersJson) {
            runOnUiThread(() -> {
                if (isNetworkAvailable()) {
                    firebaseManager.uploadCustomers(customersJson, new FirebaseManager.OnCompleteListener() {
                        @Override
                        public void onSuccess(String message) {
                            runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, message, Toast.LENGTH_SHORT).show();
                                webView.evaluateJavascript("if(window.onCustomersUploadSuccess) window.onCustomersUploadSuccess();", null);
                            });
                        }

                        @Override
                        public void onError(String error) {
                            runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, error, Toast.LENGTH_SHORT).show();
                                webView.evaluateJavascript("if(window.onCustomersUploadError) window.onCustomersUploadError('" + error + "');", null);
                            });
                        }
                    });
                } else {
                    Toast.makeText(MainActivity.this, "لا يوجد اتصال بالإنترنت", Toast.LENGTH_SHORT).show();
                }
            });
        }

        @JavascriptInterface
        public void downloadCustomers() {
            runOnUiThread(() -> {
                if (isNetworkAvailable()) {
                    firebaseManager.downloadCustomers(new FirebaseManager.OnDataListener() {
                        @Override
                        public void onDataReceived(String data) {
                            runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, "تم تحميل العملاء", Toast.LENGTH_SHORT).show();
                                webView.evaluateJavascript("if(window.onCustomersDownloadSuccess) window.onCustomersDownloadSuccess('" + data + "');", null);
                            });
                        }

                        @Override
                        public void onError(String error) {
                            runOnUiThread(() -> {
                                Toast.makeText(MainActivity.this, error, Toast.LENGTH_SHORT).show();
                                webView.evaluateJavascript("if(window.onCustomersDownloadError) window.onCustomersDownloadError('" + error + "');", null);
                            });
                        }
                    });
                } else {
                    Toast.makeText(MainActivity.this, "لا يوجد اتصال بالإنترنت", Toast.LENGTH_SHORT).show();
                }
            });
        }

        @JavascriptInterface
        public boolean isCloudConnected() {
            return firebaseManager != null && firebaseManager.isConnected() && isNetworkAvailable();
        }

        @JavascriptInterface
        public String getCloudUserId() {
            if (firebaseManager != null) {
                return firebaseManager.getUserId();
            }
            return null;
        }
    }

    /**
     * Custom WebView Client
     */
    private class CustomWebViewClient extends WebViewClient {
        
        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            swipeRefreshLayout.setRefreshing(false);
        }
        
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            if (url.startsWith("http://") || url.startsWith("https://")) {
                // Open external links in browser
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                startActivity(intent);
                return true;
            }
            return false;
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && webView.canGoBack()) {
            webView.goBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void showExitDialog() {
        new AlertDialog.Builder(this)
                .setTitle("الخروج من التطبيق")
                .setMessage("هل تريد الخروج من تطبيق النسور الماسية؟")
                .setPositiveButton("نعم", (dialog, which) -> finish())
                .setNegativeButton("لا", null)
                .show();
    }

    /**
     * فحص توفر الشبكة
     * Check network availability
     */
    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
}
