package com.diamondeagles.inventory;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

import com.google.firebase.FirebaseApp;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.storage.FirebaseStorage;
import com.google.firebase.storage.StorageReference;

import java.util.HashMap;
import java.util.Map;

/**
 * مدير Firebase لإدارة العمليات السحابية
 * Firebase Manager for cloud operations
 */
public class FirebaseManager {
    
    private static final String TAG = "FirebaseManager";
    
    // Firebase instances
    private FirebaseAuth mAuth;
    private FirebaseFirestore db;
    private FirebaseStorage storage;
    private Context context;
    
    // Collections names
    public static final String PRODUCTS_COLLECTION = "products";
    public static final String CUSTOMERS_COLLECTION = "customers";
    public static final String COMPANIES_COLLECTION = "companies";
    public static final String BACKUPS_COLLECTION = "backups";
    
    // Singleton instance
    private static FirebaseManager instance;
    
    private FirebaseManager(Context context) {
        this.context = context;
        initializeFirebase();
    }
    
    public static synchronized FirebaseManager getInstance(Context context) {
        if (instance == null) {
            instance = new FirebaseManager(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * تهيئة Firebase
     * Initialize Firebase
     */
    private void initializeFirebase() {
        try {
            // Initialize Firebase if not already initialized
            if (FirebaseApp.getApps(context).isEmpty()) {
                FirebaseApp.initializeApp(context);
            }
            
            // Initialize Firebase services
            mAuth = FirebaseAuth.getInstance();
            db = FirebaseFirestore.getInstance();
            storage = FirebaseStorage.getInstance();
            
            Log.d(TAG, "Firebase initialized successfully");
            
        } catch (Exception e) {
            Log.e(TAG, "Error initializing Firebase: " + e.getMessage());
        }
    }
    
    /**
     * تسجيل دخول مجهول
     * Anonymous sign in
     */
    public void signInAnonymously(OnCompleteListener listener) {
        mAuth.signInAnonymously()
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        Log.d(TAG, "Anonymous sign in successful");
                        FirebaseUser user = mAuth.getCurrentUser();
                        if (listener != null) {
                            listener.onSuccess("تم تسجيل الدخول بنجاح");
                        }
                    } else {
                        Log.w(TAG, "Anonymous sign in failed", task.getException());
                        if (listener != null) {
                            listener.onError("فشل في تسجيل الدخول: " + task.getException().getMessage());
                        }
                    }
                });
    }
    
    /**
     * رفع المنتجات إلى Firebase
     * Upload products to Firebase
     */
    public void uploadProducts(String productsJson, OnCompleteListener listener) {
        if (mAuth.getCurrentUser() == null) {
            signInAnonymously(new OnCompleteListener() {
                @Override
                public void onSuccess(String message) {
                    uploadProductsToFirestore(productsJson, listener);
                }
                
                @Override
                public void onError(String error) {
                    if (listener != null) listener.onError(error);
                }
            });
        } else {
            uploadProductsToFirestore(productsJson, listener);
        }
    }
    
    private void uploadProductsToFirestore(String productsJson, OnCompleteListener listener) {
        Map<String, Object> data = new HashMap<>();
        data.put("products", productsJson);
        data.put("timestamp", System.currentTimeMillis());
        data.put("userId", mAuth.getCurrentUser().getUid());
        
        db.collection(PRODUCTS_COLLECTION)
                .document("products_data")
                .set(data)
                .addOnSuccessListener(aVoid -> {
                    Log.d(TAG, "Products uploaded successfully");
                    if (listener != null) {
                        listener.onSuccess("تم رفع المنتجات بنجاح");
                    }
                })
                .addOnFailureListener(e -> {
                    Log.w(TAG, "Error uploading products", e);
                    if (listener != null) {
                        listener.onError("فشل في رفع المنتجات: " + e.getMessage());
                    }
                });
    }
    
    /**
     * تحميل المنتجات من Firebase
     * Download products from Firebase
     */
    public void downloadProducts(OnDataListener listener) {
        if (mAuth.getCurrentUser() == null) {
            signInAnonymously(new OnCompleteListener() {
                @Override
                public void onSuccess(String message) {
                    downloadProductsFromFirestore(listener);
                }
                
                @Override
                public void onError(String error) {
                    if (listener != null) listener.onError(error);
                }
            });
        } else {
            downloadProductsFromFirestore(listener);
        }
    }
    
    private void downloadProductsFromFirestore(OnDataListener listener) {
        db.collection(PRODUCTS_COLLECTION)
                .document("products_data")
                .get()
                .addOnSuccessListener(documentSnapshot -> {
                    if (documentSnapshot.exists()) {
                        String productsJson = documentSnapshot.getString("products");
                        Log.d(TAG, "Products downloaded successfully");
                        if (listener != null) {
                            listener.onDataReceived(productsJson);
                        }
                    } else {
                        Log.d(TAG, "No products data found");
                        if (listener != null) {
                            listener.onError("لا توجد بيانات منتجات");
                        }
                    }
                })
                .addOnFailureListener(e -> {
                    Log.w(TAG, "Error downloading products", e);
                    if (listener != null) {
                        listener.onError("فشل في تحميل المنتجات: " + e.getMessage());
                    }
                });
    }
    
    /**
     * رفع العملاء إلى Firebase
     * Upload customers to Firebase
     */
    public void uploadCustomers(String customersJson, OnCompleteListener listener) {
        if (mAuth.getCurrentUser() == null) {
            signInAnonymously(new OnCompleteListener() {
                @Override
                public void onSuccess(String message) {
                    uploadCustomersToFirestore(customersJson, listener);
                }
                
                @Override
                public void onError(String error) {
                    if (listener != null) listener.onError(error);
                }
            });
        } else {
            uploadCustomersToFirestore(customersJson, listener);
        }
    }
    
    private void uploadCustomersToFirestore(String customersJson, OnCompleteListener listener) {
        Map<String, Object> data = new HashMap<>();
        data.put("customers", customersJson);
        data.put("timestamp", System.currentTimeMillis());
        data.put("userId", mAuth.getCurrentUser().getUid());
        
        db.collection(CUSTOMERS_COLLECTION)
                .document("customers_data")
                .set(data)
                .addOnSuccessListener(aVoid -> {
                    Log.d(TAG, "Customers uploaded successfully");
                    if (listener != null) {
                        listener.onSuccess("تم رفع العملاء بنجاح");
                    }
                })
                .addOnFailureListener(e -> {
                    Log.w(TAG, "Error uploading customers", e);
                    if (listener != null) {
                        listener.onError("فشل في رفع العملاء: " + e.getMessage());
                    }
                });
    }
    
    /**
     * تحميل العملاء من Firebase
     * Download customers from Firebase
     */
    public void downloadCustomers(OnDataListener listener) {
        if (mAuth.getCurrentUser() == null) {
            signInAnonymously(new OnCompleteListener() {
                @Override
                public void onSuccess(String message) {
                    downloadCustomersFromFirestore(listener);
                }
                
                @Override
                public void onError(String error) {
                    if (listener != null) listener.onError(error);
                }
            });
        } else {
            downloadCustomersFromFirestore(listener);
        }
    }
    
    private void downloadCustomersFromFirestore(OnDataListener listener) {
        db.collection(CUSTOMERS_COLLECTION)
                .document("customers_data")
                .get()
                .addOnSuccessListener(documentSnapshot -> {
                    if (documentSnapshot.exists()) {
                        String customersJson = documentSnapshot.getString("customers");
                        Log.d(TAG, "Customers downloaded successfully");
                        if (listener != null) {
                            listener.onDataReceived(customersJson);
                        }
                    } else {
                        Log.d(TAG, "No customers data found");
                        if (listener != null) {
                            listener.onError("لا توجد بيانات عملاء");
                        }
                    }
                })
                .addOnFailureListener(e -> {
                    Log.w(TAG, "Error downloading customers", e);
                    if (listener != null) {
                        listener.onError("فشل في تحميل العملاء: " + e.getMessage());
                    }
                });
    }
    
    /**
     * فحص حالة الاتصال
     * Check connection status
     */
    public boolean isConnected() {
        return mAuth != null && mAuth.getCurrentUser() != null;
    }
    
    /**
     * الحصول على معرف المستخدم
     * Get user ID
     */
    public String getUserId() {
        if (mAuth != null && mAuth.getCurrentUser() != null) {
            return mAuth.getCurrentUser().getUid();
        }
        return null;
    }
    
    // Interfaces for callbacks
    public interface OnCompleteListener {
        void onSuccess(String message);
        void onError(String error);
    }
    
    public interface OnDataListener {
        void onDataReceived(String data);
        void onError(String error);
    }
}
