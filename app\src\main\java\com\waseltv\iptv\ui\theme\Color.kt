package com.waseltv.iptv.ui.theme

import androidx.compose.ui.graphics.Color

// Primary Colors - Blue Theme
val PrimaryBlue = Color(0xFF2196F3)
val PrimaryBlueDark = Color(0xFF1976D2)
val PrimaryBlueLight = Color(0xFFBBDEFB)

// Secondary Colors
val AccentBlue = Color(0xFF03DAC5)
val AccentBlueDark = Color(0xFF018786)

// Background Colors
val BackgroundWhite = Color(0xFFFFFFFF)
val BackgroundLightGray = Color(0xFFF5F5F5)
val BackgroundDarkGray = Color(0xFF424242)

// Text Colors
val TextPrimary = Color(0xFF212121)
val TextSecondary = Color(0xFF757575)
val TextWhite = Color(0xFFFFFFFF)

// Status Colors
val SuccessGreen = Color(0xFF4CAF50)
val ErrorRed = Color(0xFFF44336)
val WarningOrange = Color(0xFFFF9800)

// Card and Surface Colors
val CardBackground = Color(0xFFFFFFFF)
val SurfaceColor = Color(0xFFFAFAFA)
val DividerColor = Color(0xFFE0E0E0)

// Player Colors
val PlayerBackground = Color(0xFF000000)
val PlayerControls = Color(0x80FFFFFF)

// Material Design Colors
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)
