#!/usr/bin/env python3
"""
أداة لاختبار الاتصال بسيرفر IPTV وتحليل الاستجابة
"""

import requests
import json
from urllib.parse import urlencode

# معلومات السيرفر
SERVER_URL = "http://maventv.one:80"
USERNAME = "Odaitv"
PASSWORD = "Odai2030"

def test_server_connection():
    """اختبار الاتصال بالسيرفر"""
    print("🔍 اختبار الاتصال بسيرفر IPTV...")
    print(f"السيرفر: {SERVER_URL}")
    print(f"المستخدم: {USERNAME}")
    print("-" * 50)
    
    # قائمة الروابط للاختبار
    test_urls = [
        f"{SERVER_URL}/player_api.php?username={USERNAME}&password={PASSWORD}&action=get_live_streams",
        f"{SERVER_URL}/get.php?username={USERNAME}&password={PASSWORD}&type=m3u_plus&output=ts",
        f"{SERVER_URL}/get.php?username={USERNAME}&password={PASSWORD}&type=m3u",
        f"{SERVER_URL}/player_api.php?username={USERNAME}&password={PASSWORD}&action=get_live_categories",
        f"{SERVER_URL}/player_api.php?username={USERNAME}&password={PASSWORD}",
    ]
    
    headers = {
        'User-Agent': 'WASEL-TV/1.0',
        'Accept': '*/*',
        'Connection': 'keep-alive'
    }
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n📡 اختبار الرابط {i}:")
        print(f"URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            print(f"✅ كود الاستجابة: {response.status_code}")
            print(f"📏 حجم المحتوى: {len(response.content)} بايت")
            
            if response.status_code == 200:
                content = response.text
                print(f"📄 نوع المحتوى: {response.headers.get('content-type', 'غير محدد')}")
                
                # تحليل المحتوى
                if content.strip().startswith('[') or content.strip().startswith('{'):
                    # JSON content
                    try:
                        data = json.loads(content)
                        if isinstance(data, list):
                            print(f"📺 عدد القنوات في JSON: {len(data)}")
                            if len(data) > 0:
                                print("🎯 أول قناة:")
                                first_channel = data[0]
                                print(f"   - الاسم: {first_channel.get('name', 'غير محدد')}")
                                print(f"   - ID: {first_channel.get('stream_id', 'غير محدد')}")
                                print(f"   - الفئة: {first_channel.get('category_id', 'غير محدد')}")
                        else:
                            print(f"📋 محتوى JSON: {str(data)[:200]}...")
                    except json.JSONDecodeError:
                        print("❌ فشل في تحليل JSON")
                        print(f"📄 أول 200 حرف: {content[:200]}")
                
                elif content.strip().startswith('#EXTM3U'):
                    # M3U content
                    lines = content.split('\n')
                    extinf_count = sum(1 for line in lines if line.strip().startswith('#EXTINF:'))
                    print(f"📺 عدد القنوات في M3U: {extinf_count}")
                    
                    # عرض أول قناة
                    for i, line in enumerate(lines):
                        if line.strip().startswith('#EXTINF:'):
                            print(f"🎯 أول قناة: {line.strip()}")
                            if i + 1 < len(lines):
                                print(f"🔗 الرابط: {lines[i + 1].strip()}")
                            break
                
                else:
                    print(f"📄 نوع محتوى غير معروف. أول 200 حرف:")
                    print(content[:200])
                    
            else:
                print(f"❌ فشل الطلب: {response.status_code}")
                print(f"📄 رسالة الخطأ: {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الاتصال: {str(e)}")
        
        print("-" * 30)

def test_stream_url():
    """اختبار رابط بث مباشر"""
    print("\n🎬 اختبار رابط بث مباشر...")
    
    # جرب عدة أرقام للقنوات
    test_stream_ids = ["1", "2", "3", "100", "1001", "1002"]
    
    for stream_id in test_stream_ids:
        stream_url = f"{SERVER_URL}/live/{USERNAME}/{PASSWORD}/{stream_id}.ts"
        print(f"\n🔗 اختبار: {stream_url}")
        
        try:
            response = requests.head(stream_url, timeout=10)
            print(f"✅ كود الاستجابة: {response.status_code}")
            if response.status_code == 200:
                print(f"📏 حجم المحتوى: {response.headers.get('content-length', 'غير محدد')}")
                print(f"📄 نوع المحتوى: {response.headers.get('content-type', 'غير محدد')}")
                print("🎉 الرابط يعمل!")
                break
            else:
                print(f"❌ الرابط لا يعمل")
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ: {str(e)}")

if __name__ == "__main__":
    test_server_connection()
    test_stream_url()
    print("\n✅ انتهى الاختبار")
