// Google Drive Sync for Diamond Eagles Inventory
// مزامنة Google Drive لتطبيق النسور الماسية

console.log('🌐 تحميل نظام مزامنة Google Drive...');

// Google Drive API Configuration
const GOOGLE_DRIVE_CONFIG = {
    // 👇 استبدل هذه القيم بالقيم الحقيقية من Google Cloud Console
    CLIENT_ID: 'PASTE_YOUR_CLIENT_ID_HERE.apps.googleusercontent.com',
    API_KEY: 'PASTE_YOUR_API_KEY_HERE',
    DISCOVERY_DOC: 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest',
    SCOPES: 'https://www.googleapis.com/auth/drive.file'
    // 👆 الصق Client ID و API Key من Google Cloud Console هنا
};

// Global variables
let gapi = null;
let isGoogleDriveReady = false;
let googleUser = null;
let driveAppFolder = null;

/**
 * تهيئة Google Drive API
 * Initialize Google Drive API
 */
async function initializeGoogleDrive() {
    try {
        console.log('🔄 تهيئة Google Drive API...');
        
        // Load Google API
        await loadGoogleAPI();
        
        // Initialize gapi
        await gapi.load('auth2', initAuth);
        await gapi.load('client', initClient);
        
        console.log('✅ تم تهيئة Google Drive بنجاح');
        isGoogleDriveReady = true;
        updateCloudUI();
        showCloudStatus('متصل بـ Google Drive', 'success');
        
    } catch (error) {
        console.error('❌ فشل في تهيئة Google Drive:', error);
        isGoogleDriveReady = false;
        showCloudStatus('فشل الاتصال بـ Google Drive', 'error');
    }
}

/**
 * تحميل Google API
 * Load Google API
 */
function loadGoogleAPI() {
    return new Promise((resolve, reject) => {
        if (window.gapi) {
            gapi = window.gapi;
            resolve();
            return;
        }
        
        const script = document.createElement('script');
        script.src = 'https://apis.google.com/js/api.js';
        script.onload = () => {
            gapi = window.gapi;
            resolve();
        };
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

/**
 * تهيئة المصادقة
 * Initialize Authentication
 */
function initAuth() {
    return gapi.auth2.init({
        client_id: GOOGLE_DRIVE_CONFIG.CLIENT_ID
    });
}

/**
 * تهيئة العميل
 * Initialize Client
 */
async function initClient() {
    await gapi.client.init({
        apiKey: GOOGLE_DRIVE_CONFIG.API_KEY,
        clientId: GOOGLE_DRIVE_CONFIG.CLIENT_ID,
        discoveryDocs: [GOOGLE_DRIVE_CONFIG.DISCOVERY_DOC],
        scope: GOOGLE_DRIVE_CONFIG.SCOPES
    });
}

/**
 * تسجيل الدخول إلى Google
 * Sign in to Google
 */
async function signInToGoogle() {
    try {
        console.log('🔐 تسجيل الدخول إلى Google...');
        
        const authInstance = gapi.auth2.getAuthInstance();
        googleUser = await authInstance.signIn();
        
        console.log('✅ تم تسجيل الدخول بنجاح');
        console.log('👤 المستخدم:', googleUser.getBasicProfile().getName());
        
        // Create app folder if not exists
        await createAppFolder();
        
        isCloudEnabled = true;
        cloudUserId = googleUser.getBasicProfile().getId();
        updateCloudUI();
        showCloudStatus('متصل بـ Google Drive', 'success');
        
        return true;
        
    } catch (error) {
        console.error('❌ فشل في تسجيل الدخول:', error);
        showCloudStatus('فشل في تسجيل الدخول', 'error');
        return false;
    }
}

/**
 * إنشاء مجلد التطبيق في Google Drive
 * Create app folder in Google Drive
 */
async function createAppFolder() {
    try {
        // Check if folder exists
        const response = await gapi.client.drive.files.list({
            q: "name='النسور الماسية' and mimeType='application/vnd.google-apps.folder'",
            spaces: 'drive'
        });
        
        if (response.result.files.length > 0) {
            driveAppFolder = response.result.files[0];
            console.log('📁 تم العثور على مجلد التطبيق');
        } else {
            // Create folder
            const folderResponse = await gapi.client.drive.files.create({
                resource: {
                    name: 'النسور الماسية',
                    mimeType: 'application/vnd.google-apps.folder'
                }
            });
            
            driveAppFolder = folderResponse.result;
            console.log('📁 تم إنشاء مجلد التطبيق');
        }
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء المجلد:', error);
    }
}

/**
 * رفع المنتجات إلى Google Drive
 * Upload products to Google Drive
 */
async function uploadProductsToDrive() {
    if (!isGoogleDriveReady || !googleUser) {
        showToast('يجب تسجيل الدخول إلى Google أولاً');
        return;
    }
    
    try {
        console.log('📤 رفع المنتجات إلى Google Drive...');
        syncInProgress = true;
        updateSyncUI(true);
        
        const productsData = {
            products: products,
            timestamp: Date.now(),
            userId: googleUser.getBasicProfile().getId(),
            appVersion: '2.0'
        };
        
        const fileContent = JSON.stringify(productsData, null, 2);
        const fileName = `products_${new Date().toISOString().split('T')[0]}.json`;
        
        // Upload file
        const fileMetadata = {
            name: fileName,
            parents: [driveAppFolder.id]
        };
        
        const form = new FormData();
        form.append('metadata', new Blob([JSON.stringify(fileMetadata)], {type: 'application/json'}));
        form.append('file', new Blob([fileContent], {type: 'application/json'}));
        
        const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
            method: 'POST',
            headers: new Headers({
                'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
            }),
            body: form
        });
        
        if (response.ok) {
            console.log('✅ تم رفع المنتجات بنجاح');
            showToast('تم رفع المنتجات إلى Google Drive بنجاح', 'success');
            lastSyncTime = new Date().toLocaleString('ar-SA');
            localStorage.setItem('lastSyncTime', lastSyncTime);
        } else {
            throw new Error('فشل في رفع الملف');
        }
        
    } catch (error) {
        console.error('❌ خطأ في رفع المنتجات:', error);
        showToast('فشل في رفع المنتجات: ' + error.message, 'error');
    } finally {
        syncInProgress = false;
        updateSyncUI(false);
    }
}

/**
 * تحميل المنتجات من Google Drive
 * Download products from Google Drive
 */
async function downloadProductsFromDrive() {
    if (!isGoogleDriveReady || !googleUser) {
        showToast('يجب تسجيل الدخول إلى Google أولاً');
        return;
    }
    
    try {
        console.log('📥 تحميل المنتجات من Google Drive...');
        syncInProgress = true;
        updateSyncUI(true);
        
        // Find latest products file
        const response = await gapi.client.drive.files.list({
            q: `name contains 'products_' and parents in '${driveAppFolder.id}'`,
            orderBy: 'createdTime desc',
            pageSize: 1
        });
        
        if (response.result.files.length === 0) {
            showToast('لا توجد ملفات منتجات في Google Drive', 'warning');
            return;
        }
        
        const file = response.result.files[0];
        
        // Download file content
        const fileResponse = await gapi.client.drive.files.get({
            fileId: file.id,
            alt: 'media'
        });
        
        const productsData = JSON.parse(fileResponse.body);
        
        if (productsData.products && Array.isArray(productsData.products)) {
            products = productsData.products;
            saveProducts();
            displayProducts();
            updateDashboard();
            
            console.log('✅ تم تحميل المنتجات بنجاح');
            showToast(`تم تحميل ${products.length} منتج من Google Drive`, 'success');
            lastSyncTime = new Date().toLocaleString('ar-SA');
            localStorage.setItem('lastSyncTime', lastSyncTime);
        } else {
            throw new Error('تنسيق ملف غير صحيح');
        }
        
    } catch (error) {
        console.error('❌ خطأ في تحميل المنتجات:', error);
        showToast('فشل في تحميل المنتجات: ' + error.message, 'error');
    } finally {
        syncInProgress = false;
        updateSyncUI(false);
    }
}

/**
 * رفع العملاء إلى Google Drive
 * Upload customers to Google Drive
 */
async function uploadCustomersToDrive() {
    if (!isGoogleDriveReady || !googleUser) {
        showToast('يجب تسجيل الدخول إلى Google أولاً');
        return;
    }
    
    try {
        console.log('📤 رفع العملاء إلى Google Drive...');
        syncInProgress = true;
        updateSyncUI(true);
        
        const customersData = {
            customers: customers,
            timestamp: Date.now(),
            userId: googleUser.getBasicProfile().getId(),
            appVersion: '2.0'
        };
        
        const fileContent = JSON.stringify(customersData, null, 2);
        const fileName = `customers_${new Date().toISOString().split('T')[0]}.json`;
        
        // Upload file
        const fileMetadata = {
            name: fileName,
            parents: [driveAppFolder.id]
        };
        
        const form = new FormData();
        form.append('metadata', new Blob([JSON.stringify(fileMetadata)], {type: 'application/json'}));
        form.append('file', new Blob([fileContent], {type: 'application/json'}));
        
        const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
            method: 'POST',
            headers: new Headers({
                'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
            }),
            body: form
        });
        
        if (response.ok) {
            console.log('✅ تم رفع العملاء بنجاح');
            showToast('تم رفع العملاء إلى Google Drive بنجاح', 'success');
            lastSyncTime = new Date().toLocaleString('ar-SA');
            localStorage.setItem('lastSyncTime', lastSyncTime);
        } else {
            throw new Error('فشل في رفع الملف');
        }
        
    } catch (error) {
        console.error('❌ خطأ في رفع العملاء:', error);
        showToast('فشل في رفع العملاء: ' + error.message, 'error');
    } finally {
        syncInProgress = false;
        updateSyncUI(false);
    }
}

/**
 * تحميل العملاء من Google Drive
 * Download customers from Google Drive
 */
async function downloadCustomersFromDrive() {
    if (!isGoogleDriveReady || !googleUser) {
        showToast('يجب تسجيل الدخول إلى Google أولاً');
        return;
    }
    
    try {
        console.log('📥 تحميل العملاء من Google Drive...');
        syncInProgress = true;
        updateSyncUI(true);
        
        // Find latest customers file
        const response = await gapi.client.drive.files.list({
            q: `name contains 'customers_' and parents in '${driveAppFolder.id}'`,
            orderBy: 'createdTime desc',
            pageSize: 1
        });
        
        if (response.result.files.length === 0) {
            showToast('لا توجد ملفات عملاء في Google Drive', 'warning');
            return;
        }
        
        const file = response.result.files[0];
        
        // Download file content
        const fileResponse = await gapi.client.drive.files.get({
            fileId: file.id,
            alt: 'media'
        });
        
        const customersData = JSON.parse(fileResponse.body);
        
        if (customersData.customers && Array.isArray(customersData.customers)) {
            customers = customersData.customers;
            saveCustomers();
            displayCustomers();
            updateDashboard();
            
            console.log('✅ تم تحميل العملاء بنجاح');
            showToast(`تم تحميل ${customers.length} عميل من Google Drive`, 'success');
            lastSyncTime = new Date().toLocaleString('ar-SA');
            localStorage.setItem('lastSyncTime', lastSyncTime);
        } else {
            throw new Error('تنسيق ملف غير صحيح');
        }
        
    } catch (error) {
        console.error('❌ خطأ في تحميل العملاء:', error);
        showToast('فشل في تحميل العملاء: ' + error.message, 'error');
    } finally {
        syncInProgress = false;
        updateSyncUI(false);
    }
}

/**
 * مزامنة شاملة مع Google Drive
 * Full sync with Google Drive
 */
async function performGoogleDriveSync() {
    if (!isGoogleDriveReady || !googleUser) {
        showToast('يجب تسجيل الدخول إلى Google أولاً');
        return;
    }
    
    try {
        console.log('🔄 بدء المزامنة الشاملة مع Google Drive...');
        
        // Upload products
        await uploadProductsToDrive();
        
        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Upload customers
        await uploadCustomersToDrive();
        
        showToast('تمت المزامنة الشاملة مع Google Drive بنجاح', 'success');
        
    } catch (error) {
        console.error('❌ خطأ في المزامنة الشاملة:', error);
        showToast('فشل في المزامنة الشاملة: ' + error.message, 'error');
    }
}

// Override cloud functions to use Google Drive
window.uploadProductsToCloud = uploadProductsToDrive;
window.downloadProductsFromCloud = downloadProductsFromDrive;
window.uploadCustomersToCloud = uploadCustomersToDrive;
window.downloadCustomersFromCloud = downloadCustomersFromDrive;
window.performFullSync = performGoogleDriveSync;

// Initialize Google Drive when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 تهيئة نظام Google Drive...');
    
    // Add sign-in button to UI
    setTimeout(() => {
        addGoogleSignInButton();
    }, 2000);
});

/**
 * إضافة زر تسجيل الدخول إلى Google
 * Add Google sign-in button
 */
function addGoogleSignInButton() {
    // Add to cloud actions in settings
    const cloudActions = document.querySelector('.cloud-actions');
    if (cloudActions) {
        const signInBtn = document.createElement('button');
        signInBtn.type = 'button';
        signInBtn.className = 'btn btn-primary';
        signInBtn.innerHTML = '<i class="fab fa-google"></i> تسجيل دخول Google';
        signInBtn.onclick = async () => {
            await initializeGoogleDrive();
            await signInToGoogle();
        };
        
        cloudActions.appendChild(signInBtn);
    }
}

console.log('✅ تم تحميل نظام Google Drive بنجاح');
