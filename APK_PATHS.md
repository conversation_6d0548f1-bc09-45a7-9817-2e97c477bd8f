# 📱 مسارات ملف APK - تطبيق واصل TV

## 📍 **المسار الكامل المتوقع:**

```
C:\Users\<USER>\OneDrive\Desktop\اندرويد جديد\app\build\outputs\apk\debug\app-debug.apk
```

## 📂 **هيكل المجلدات:**

```
📁 اندرويد جديد/                    ← مجلد المشروع الرئيسي
├── 📁 app/                          ← مجلد التطبيق
│   ├── 📁 build/                    ← مجلد البناء (ينشأ بعد gradlew)
│   │   └── 📁 outputs/              ← مخرجات البناء
│   │       └── 📁 apk/              ← ملفات APK
│   │           ├── 📁 debug/        ← إصدار التطوير
│   │           │   └── 📄 app-debug.apk  ← الملف المطلوب!
│   │           └── 📁 release/      ← إصدار الإنتاج
│   │               └── 📄 app-release.apk
│   ├── 📁 src/                      ← كود المصدر
│   └── 📄 build.gradle              ← إعدادات البناء
├── 📄 build_apk.bat                 ← سكريبت البناء
├── 📄 find_apk.bat                  ← سكريبت البحث
└── 📄 gradlew.bat                   ← أداة Gradle
```

---

## 🔧 **خطوات الحصول على ملف APK:**

### **1. تشغيل سكريبت البناء:**
```bash
# في مجلد المشروع
build_apk.bat
```

### **2. أو بناء يدوي:**
```bash
# تنظيف المشروع
gradlew clean

# بناء APK
gradlew assembleDebug
```

### **3. البحث عن الملف:**
```bash
# تشغيل سكريبت البحث
find_apk.bat
```

---

## 📍 **مسارات بديلة محتملة:**

### **إذا لم تجد الملف في المسار الأساسي، ابحث في:**

```
📁 المشروع\app\build\outputs\apk\debug\
📁 المشروع\app\build\outputs\apk\
📁 المشروع\build\outputs\apk\debug\
📁 المشروع\build\outputs\apk\
```

### **أسماء ملفات محتملة:**
```
app-debug.apk           ← الاسم المعتاد
app-debug-unsigned.apk  ← غير موقع
WASEL-TV-debug.apk      ← باسم التطبيق
com.waseltv.iptv.apk    ← باسم الحزمة
```

---

## 🔍 **طرق البحث اليدوي:**

### **1. في Windows Explorer:**
- اذهب إلى مجلد المشروع
- ابحث عن "*.apk" في شريط البحث
- أو اذهب يدوياً إلى: `app\build\outputs\apk\debug\`

### **2. في Command Prompt:**
```cmd
# في مجلد المشروع
dir /s *.apk
```

### **3. في PowerShell:**
```powershell
# البحث عن جميع ملفات APK
Get-ChildItem -Recurse -Filter "*.apk"
```

---

## ⚠️ **إذا لم يوجد مجلد build:**

هذا يعني أن التطبيق لم يتم بناؤه بعد. اتبع هذه الخطوات:

### **1. تأكد من وجود Android SDK:**
```
ANDROID_HOME = C:\Users\<USER>\AppData\Local\Android\Sdk
```

### **2. تأكد من وجود Java:**
```cmd
java -version
```

### **3. بناء التطبيق:**
```cmd
gradlew assembleDebug
```

---

## 📱 **معلومات ملف APK المتوقع:**

```
📄 اسم الملف: app-debug.apk
📏 الحجم: ~15-20 MB
📅 تاريخ الإنشاء: بعد تشغيل gradlew
🏷️ النوع: Android Application Package
📦 Package: com.waseltv.iptv.debug
🔢 الإصدار: 1.0.0
```

---

## 🚀 **بعد العثور على الملف:**

### **1. نسخ إلى الجهاز:**
- انسخ `app-debug.apk` إلى جهاز الأندرويد
- ضعه في مجلد `Downloads` أو `Documents`

### **2. تثبيت على الجهاز:**
- افتح مدير الملفات
- اضغط على ملف APK
- اضغط "تثبيت"

### **3. أو استخدم ADB:**
```cmd
adb install "C:\Users\<USER>\OneDrive\Desktop\اندرويد جديد\app\build\outputs\apk\debug\app-debug.apk"
```

---

## 📞 **إذا واجهت مشاكل:**

1. **تشغيل سكريبت البحث:** `find_apk.bat`
2. **تشغيل سكريبت البناء:** `build_apk.bat`
3. **فحص logs:** راجع رسائل الخطأ في Command Prompt
4. **التأكد من المتطلبات:** Android Studio, Java, Android SDK

**الملف سيكون في المسار المذكور أعلاه بعد بناء التطبيق بنجاح!** 📱✨
