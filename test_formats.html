<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنسيقات Stream - WASEL-TV</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2196F3;
            margin-bottom: 30px;
        }
        
        .format-test {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        
        .format-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .format-name {
            font-size: 18px;
            font-weight: bold;
            color: #2196F3;
        }
        
        .test-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-btn:hover {
            background: #45a049;
        }
        
        .test-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .url-display {
            font-family: monospace;
            background: #f0f0f0;
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
            word-break: break-all;
            margin: 8px 0;
        }
        
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-weight: bold;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .result.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .summary {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .test-all-btn {
            background: #FF5722;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
        }
        
        .test-all-btn:hover {
            background: #E64A19;
        }
        
        .info-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار تنسيقات Stream - WASEL-TV</h1>
        
        <div class="info-box">
            <h3>🎯 حل مشكلة "تنسيق غير مدعوم"</h3>
            <p>هذه الأداة تختبر تنسيقات مختلفة لنفس الـ stream لمعرفة أيها يعمل</p>
        </div>
        
        <button class="test-all-btn" onclick="testAllFormats()">🚀 اختبار جميع التنسيقات</button>
        
        <div id="format-tests">
            <!-- سيتم إضافة اختبارات التنسيقات هنا -->
        </div>
        
        <div class="summary" id="summary" style="display: none;">
            <h3>📊 ملخص النتائج</h3>
            <div id="summary-content"></div>
        </div>
    </div>

    <script>
        // معلومات سيرفر واصل TV
        const PLAYLIST_NAME = 'واصل TV';
        const SERVER_URL = 'http://maventv.one:80';
        const USERNAME = 'odaitv';
        const PASSWORD = 'Odai2030';
        const STREAM_ID = '106997'; // القرآن الكريم
        
        // تنسيقات مختلفة للاختبار
        const formats = [
            {
                name: 'TS (Transport Stream)',
                url: `${SERVER_URL}/live/${USERNAME}/${PASSWORD}/${STREAM_ID}.ts`,
                description: 'التنسيق الأصلي من Xtream Codes'
            },
            {
                name: 'HLS (M3U8)',
                url: `${SERVER_URL}/live/${USERNAME}/${PASSWORD}/${STREAM_ID}.m3u8`,
                description: 'تنسيق HLS للتوافق الأفضل'
            },
            {
                name: 'MP4',
                url: `${SERVER_URL}/live/${USERNAME}/${PASSWORD}/${STREAM_ID}.mp4`,
                description: 'تنسيق MP4 للتوافق العام'
            },
            {
                name: 'Direct Stream',
                url: `${SERVER_URL}/live/${USERNAME}/${PASSWORD}/${STREAM_ID}`,
                description: 'بدون امتداد ملف'
            },
            {
                name: 'HLS Alternative',
                url: `${SERVER_URL}/hls/${USERNAME}/${PASSWORD}/${STREAM_ID}.m3u8`,
                description: 'مسار HLS بديل'
            },
            {
                name: 'Streaming Path',
                url: `${SERVER_URL}/streaming/${USERNAME}/${PASSWORD}/${STREAM_ID}.ts`,
                description: 'مسار streaming بديل'
            },
            {
                name: 'With Token',
                url: `${SERVER_URL}/live/${USERNAME}/${PASSWORD}/${STREAM_ID}.ts?token=free`,
                description: 'مع token إضافي'
            },
            {
                name: 'Test Video (مضمون)',
                url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                description: 'فيديو تجريبي مضمون التشغيل'
            }
        ];
        
        let testResults = {};
        
        function createFormatTest(format, index) {
            return `
                <div class="format-test" id="test-${index}">
                    <div class="format-header">
                        <div class="format-name">${format.name}</div>
                        <button class="test-btn" onclick="testFormat(${index})">اختبار</button>
                    </div>
                    <p>${format.description}</p>
                    <div class="url-display">${format.url}</div>
                    <div id="result-${index}"></div>
                </div>
            `;
        }
        
        function displayFormatTests() {
            const container = document.getElementById('format-tests');
            container.innerHTML = formats.map(createFormatTest).join('');
        }
        
        async function testFormat(index) {
            const format = formats[index];
            const resultDiv = document.getElementById(`result-${index}`);
            const btn = document.querySelector(`#test-${index} .test-btn`);
            
            btn.disabled = true;
            btn.textContent = 'جاري الاختبار...';
            
            resultDiv.innerHTML = '<div class="result loading">🔄 جاري اختبار التنسيق...</div>';
            
            const startTime = Date.now();
            
            try {
                // محاولة الوصول للـ URL
                const response = await fetch(format.url, {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                
                const duration = Date.now() - startTime;
                
                testResults[index] = {
                    format: format.name,
                    success: true,
                    duration: duration,
                    message: 'تم الاتصال بنجاح'
                };
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ نجح الاتصال!<br>
                        <small>الوقت: ${duration}ms</small>
                    </div>
                `;
                
            } catch (error) {
                const duration = Date.now() - startTime;
                
                testResults[index] = {
                    format: format.name,
                    success: false,
                    duration: duration,
                    message: error.message
                };
                
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ فشل الاتصال<br>
                        <small>الخطأ: ${error.message}</small><br>
                        <small>الوقت: ${duration}ms</small>
                    </div>
                `;
            }
            
            btn.disabled = false;
            btn.textContent = 'اختبار';
            
            updateSummary();
        }
        
        async function testAllFormats() {
            const btn = document.querySelector('.test-all-btn');
            btn.disabled = true;
            btn.textContent = '🔄 جاري اختبار جميع التنسيقات...';
            
            testResults = {};
            
            for (let i = 0; i < formats.length; i++) {
                await testFormat(i);
                // تأخير بسيط بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            btn.disabled = false;
            btn.textContent = '🚀 اختبار جميع التنسيقات';
        }
        
        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            const contentDiv = document.getElementById('summary-content');
            
            if (Object.keys(testResults).length === 0) {
                summaryDiv.style.display = 'none';
                return;
            }
            
            const successCount = Object.values(testResults).filter(r => r.success).length;
            const totalCount = Object.keys(testResults).length;
            
            const successFormats = Object.values(testResults)
                .filter(r => r.success)
                .map(r => r.format)
                .join(', ');
            
            const failedFormats = Object.values(testResults)
                .filter(r => !r.success)
                .map(r => r.format)
                .join(', ');
            
            contentDiv.innerHTML = `
                <p><strong>✅ تنسيقات تعمل (${successCount}/${totalCount}):</strong><br>
                ${successFormats || 'لا يوجد'}</p>
                
                <p><strong>❌ تنسيقات لا تعمل (${totalCount - successCount}/${totalCount}):</strong><br>
                ${failedFormats || 'لا يوجد'}</p>
                
                <p><strong>💡 التوصية:</strong><br>
                ${successCount > 0 ? 
                    'استخدم التنسيقات التي تعمل في التطبيق' : 
                    'جرب streams مختلفة أو تحقق من الاتصال بالإنترنت'
                }</p>
            `;
            
            summaryDiv.style.display = 'block';
        }
        
        // تحميل اختبارات التنسيقات عند تحميل الصفحة
        window.onload = function() {
            displayFormatTests();
            
            setTimeout(() => {
                alert('🔧 اختبار تنسيقات Stream\n\n' +
                      'هذه الأداة تختبر تنسيقات مختلفة لحل مشكلة "تنسيق غير مدعوم".\n\n' +
                      '📋 التنسيقات المختبرة:\n' +
                      '• TS (Transport Stream)\n' +
                      '• HLS (M3U8)\n' +
                      '• MP4\n' +
                      '• مسارات بديلة\n' +
                      '• فيديو تجريبي مضمون\n\n' +
                      'اضغط "اختبار جميع التنسيقات" للبدء!');
            }, 500);
        };
    </script>
</body>
</html>
