// Auto Sync System for Diamond Eagles Inventory
// نظام المزامنة التلقائية لتطبيق النسور الماسية

console.log('🔄 تحميل نظام المزامنة التلقائية...');

// Cloud-Only Auto Sync Configuration
const AUTO_SYNC_CONFIG = {
    enabled: true,
    interval: 30000, // 30 seconds
    onDataChange: true, // Sync when data changes
    showNotifications: true,
    maxRetries: 3,
    cloudOnly: true, // Only use cloud storage, no local backups
    autoSaveToCloud: true // Automatically save to cloud on data change
};

let autoSyncInterval = null;
let lastSyncHash = '';
let syncInProgress = false;

/**
 * تهيئة نظام التخزين السحابي التلقائي
 * Initialize Cloud-Only Auto Sync System
 */
function initializeAutoSync() {
    console.log('☁️ تهيئة نظام التخزين السحابي التلقائي...');

    // Check if Google Drive is available
    if (typeof window.webFirebaseManager === 'undefined' && typeof gapi === 'undefined') {
        console.log('⚠️ Google Drive غير متاح - التخزين السحابي معطل');
        showSyncNotification('يرجى تسجيل الدخول إلى Google Drive', 'warning');
        return;
    }

    // Start auto sync interval for cloud storage
    if (AUTO_SYNC_CONFIG.enabled) {
        startAutoSyncInterval();
    }

    // Listen for data changes and auto-save to cloud
    if (AUTO_SYNC_CONFIG.onDataChange) {
        setupCloudDataChangeListeners();
    }

    // Initial cloud sync check
    setTimeout(() => {
        checkForCloudUpdates();
    }, 5000);

    // Disable local storage auto-save
    disableLocalAutoSave();

    console.log('✅ تم تهيئة نظام التخزين السحابي التلقائي');
    showSyncNotification('نظام التخزين السحابي نشط', 'success');
}

/**
 * بدء المزامنة التلقائية المجدولة
 * Start Auto Sync Interval
 */
function startAutoSyncInterval() {
    if (autoSyncInterval) {
        clearInterval(autoSyncInterval);
    }
    
    autoSyncInterval = setInterval(() => {
        if (!syncInProgress) {
            checkForUpdates();
        }
    }, AUTO_SYNC_CONFIG.interval);
    
    console.log(`🔄 بدء المزامنة التلقائية كل ${AUTO_SYNC_CONFIG.interval / 1000} ثانية`);
}

/**
 * إيقاف المزامنة التلقائية
 * Stop Auto Sync
 */
function stopAutoSync() {
    if (autoSyncInterval) {
        clearInterval(autoSyncInterval);
        autoSyncInterval = null;
    }
    console.log('⏹️ تم إيقاف المزامنة التلقائية');
}

/**
 * فحص التحديثات من السحابة فقط
 * Check for updates from cloud only
 */
async function checkForCloudUpdates() {
    if (syncInProgress) return;

    try {
        syncInProgress = true;

        // Check if user is connected to Google Drive
        if (!isGoogleDriveConnected()) {
            console.log('⚠️ غير متصل بـ Google Drive');
            return;
        }

        console.log('☁️ فحص التحديثات من السحابة...');

        // Get current data hash
        const currentHash = generateDataHash();

        // Check if data changed locally
        if (currentHash !== lastSyncHash) {
            console.log('📤 تم اكتشاف تغييرات محلية - رفع للسحابة...');
            await uploadDataToCloud();
            lastSyncHash = currentHash;

            if (AUTO_SYNC_CONFIG.showNotifications) {
                showSyncNotification('تم حفظ البيانات في السحابة', 'success');
            }
        }

        // Check for remote updates from other users
        await downloadUpdatesFromCloud();

    } catch (error) {
        console.error('❌ خطأ في فحص التحديثات السحابية:', error);
        if (AUTO_SYNC_CONFIG.showNotifications) {
            showSyncNotification('خطأ في المزامنة السحابية', 'error');
        }
    } finally {
        syncInProgress = false;
    }
}

/**
 * تعطيل الحفظ المحلي التلقائي
 * Disable local auto-save
 */
function disableLocalAutoSave() {
    // Clear any existing local auto-save intervals
    if (typeof window.autoSaveInterval !== 'undefined') {
        clearInterval(window.autoSaveInterval);
        window.autoSaveInterval = null;
    }

    // Override localStorage save functions to redirect to cloud
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
        // Still save locally for immediate access
        originalSetItem.call(this, key, value);

        // But also trigger cloud save for important data
        if (key === 'products' || key === 'customers') {
            scheduleCloudSync();
        }
    };

    console.log('🚫 تم تعطيل النسخ الاحتياطية المحلية');
}

/**
 * رفع البيانات للسحابة
 * Upload data to cloud
 */
async function uploadDataToCloud() {
    try {
        // Upload products
        if (typeof uploadProductsToDrive === 'function') {
            await uploadProductsToDrive();
        }
        
        // Upload customers
        if (typeof uploadCustomersToDrive === 'function') {
            await uploadCustomersToDrive();
        }
        
        console.log('✅ تم رفع البيانات بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في رفع البيانات:', error);
        throw error;
    }
}

/**
 * تحميل التحديثات من السحابة
 * Download updates from cloud
 */
async function downloadUpdatesFromCloud() {
    try {
        let hasUpdates = false;
        
        // Check for product updates
        if (typeof downloadProductsFromDrive === 'function') {
            const productsBefore = products.length;
            await downloadProductsFromDrive();
            if (products.length !== productsBefore) {
                hasUpdates = true;
            }
        }
        
        // Check for customer updates
        if (typeof downloadCustomersFromDrive === 'function') {
            const customersBefore = customers.length;
            await downloadCustomersFromDrive();
            if (customers.length !== customersBefore) {
                hasUpdates = true;
            }
        }
        
        if (hasUpdates) {
            console.log('📥 تم تحميل تحديثات جديدة');
            
            // Update UI
            if (typeof displayProducts === 'function') displayProducts();
            if (typeof displayCustomers === 'function') displayCustomers();
            if (typeof updateDashboard === 'function') updateDashboard();
            
            if (AUTO_SYNC_CONFIG.showNotifications) {
                showSyncNotification('تم تحميل تحديثات جديدة', 'info');
            }
            
            // Update hash
            lastSyncHash = generateDataHash();
        }
        
    } catch (error) {
        console.error('❌ خطأ في تحميل التحديثات:', error);
    }
}

/**
 * إنشاء hash للبيانات الحالية
 * Generate hash for current data
 */
function generateDataHash() {
    const data = {
        products: products || [],
        customers: customers || []
    };
    
    const dataString = JSON.stringify(data);
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
        const char = dataString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString();
}

/**
 * إعداد مستمعات تغيير البيانات للسحابة
 * Setup cloud data change listeners
 */
function setupCloudDataChangeListeners() {
    // Override addProduct function for cloud sync
    const originalAddProduct = window.addProduct;
    if (originalAddProduct) {
        window.addProduct = function(...args) {
            const result = originalAddProduct.apply(this, args);
            scheduleCloudSync();
            showSyncNotification('تم إضافة منتج - جاري الحفظ في السحابة...', 'info');
            return result;
        };
    }

    // Override addCustomer function for cloud sync
    const originalAddCustomer = window.addCustomer;
    if (originalAddCustomer) {
        window.addCustomer = function(...args) {
            const result = originalAddCustomer.apply(this, args);
            scheduleCloudSync();
            showSyncNotification('تم إضافة عميل - جاري الحفظ في السحابة...', 'info');
            return result;
        };
    }

    // Override delete functions for cloud sync
    const originalDeleteProduct = window.deleteProduct;
    if (originalDeleteProduct) {
        window.deleteProduct = function(...args) {
            const result = originalDeleteProduct.apply(this, args);
            scheduleCloudSync();
            showSyncNotification('تم حذف منتج - جاري التحديث في السحابة...', 'info');
            return result;
        };
    }

    const originalDeleteCustomer = window.deleteCustomer;
    if (originalDeleteCustomer) {
        window.deleteCustomer = function(...args) {
            const result = originalDeleteCustomer.apply(this, args);
            scheduleCloudSync();
            showSyncNotification('تم حذف عميل - جاري التحديث في السحابة...', 'info');
            return result;
        };
    }

    console.log('☁️ تم إعداد مستمعات التخزين السحابي');
}

/**
 * جدولة المزامنة السحابية
 * Schedule cloud sync
 */
function scheduleCloudSync() {
    setTimeout(() => {
        if (!syncInProgress) {
            checkForCloudUpdates();
        }
    }, 1000); // Wait 1 second after data change for immediate cloud sync
}

/**
 * جدولة المزامنة (للتوافق مع النظام القديم)
 * Schedule sync (for backward compatibility)
 */
function scheduleSync() {
    scheduleCloudSync();
}

/**
 * فحص اتصال Google Drive
 * Check Google Drive connection
 */
function isGoogleDriveConnected() {
    if (typeof gapi !== 'undefined' && gapi.auth2) {
        const authInstance = gapi.auth2.getAuthInstance();
        return authInstance && authInstance.isSignedIn.get();
    }
    return false;
}

/**
 * عرض إشعار المزامنة
 * Show sync notification
 */
function showSyncNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `sync-notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-sync-alt"></i>
        <span>${message}</span>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * تبديل المزامنة التلقائية
 * Toggle auto sync
 */
function toggleAutoSync() {
    AUTO_SYNC_CONFIG.enabled = !AUTO_SYNC_CONFIG.enabled;
    
    if (AUTO_SYNC_CONFIG.enabled) {
        startAutoSyncInterval();
        showSyncNotification('تم تفعيل المزامنة التلقائية', 'success');
    } else {
        stopAutoSync();
        showSyncNotification('تم إيقاف المزامنة التلقائية', 'warning');
    }
    
    // Save setting
    localStorage.setItem('autoSyncEnabled', AUTO_SYNC_CONFIG.enabled);
}

/**
 * مزامنة سحابية يدوية فورية
 * Manual immediate cloud sync
 */
async function performManualSync() {
    if (syncInProgress) {
        showSyncNotification('المزامنة السحابية قيد التقدم بالفعل', 'warning');
        return;
    }

    if (!isGoogleDriveConnected()) {
        showSyncNotification('يرجى تسجيل الدخول إلى Google Drive أولاً', 'warning');
        return;
    }

    showSyncNotification('بدء المزامنة السحابية اليدوية...', 'info');

    try {
        await checkForCloudUpdates();
        showSyncNotification('تمت المزامنة السحابية بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في المزامنة اليدوية:', error);
        showSyncNotification('فشل في المزامنة السحابية', 'error');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Load saved settings
    const savedAutoSync = localStorage.getItem('autoSyncEnabled');
    if (savedAutoSync !== null) {
        AUTO_SYNC_CONFIG.enabled = savedAutoSync === 'true';
    }

    // Initialize cloud-only system after other systems are ready
    setTimeout(() => {
        initializeAutoSync();
    }, 3000);

    // Show initial message about cloud-only storage
    setTimeout(() => {
        showSyncNotification('نظام التخزين السحابي نشط - لا حاجة للنسخ المحلية', 'info');
    }, 5000);
});

// Export functions for global use
window.toggleAutoSync = toggleAutoSync;
window.performManualSync = performManualSync;
window.stopAutoSync = stopAutoSync;
window.scheduleCloudSync = scheduleCloudSync;
window.checkForCloudUpdates = checkForCloudUpdates;

console.log('☁️ تم تحميل نظام التخزين السحابي التلقائي بنجاح');
