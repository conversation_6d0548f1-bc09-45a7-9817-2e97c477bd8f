<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Stream URLs - WASEL-TV</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2196F3;
            margin-bottom: 30px;
        }
        
        .stream-test {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .stream-info {
            margin-bottom: 10px;
        }
        
        .stream-url {
            font-family: monospace;
            background: #f0f0f0;
            padding: 5px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
        }
        
        .test-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .test-btn:hover {
            background: #1976D2;
        }
        
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.testing {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .test-all-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
            display: block;
            margin: 0 auto 30px auto;
        }
        
        .test-all-btn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار Stream URLs - WASEL-TV</h1>
        
        <button class="test-all-btn" onclick="testAllStreams()">اختبار جميع الـ Streams</button>
        
        <div id="streams-container">
            <!-- سيتم إضافة الـ streams هنا بواسطة JavaScript -->
        </div>
    </div>

    <script>
        // قائمة الـ streams للاختبار
        const streams = [
            // قنوات الأخبار
            { id: '106997', name: 'القرآن الكريم', category: 'أخبار' },
            { id: '106998', name: 'الجزيرة مباشر', category: 'أخبار' },
            { id: '106999', name: 'العربية', category: 'أخبار' },
            { id: '107000', name: 'BBC Arabic', category: 'أخبار' },
            { id: '107001', name: 'سكاي نيوز عربية', category: 'أخبار' },
            
            // قنوات الرياضة
            { id: '107010', name: 'بي إن سبورت 1 HD', category: 'رياضة' },
            { id: '107011', name: 'بي إن سبورت 2 HD', category: 'رياضة' },
            { id: '107012', name: 'بي إن سبورت 3 HD', category: 'رياضة' },
            
            // قنوات الأطفال
            { id: '107020', name: 'سبيستون', category: 'أطفال' },
            { id: '107021', name: 'طيور الجنة', category: 'أطفال' },
            
            // قنوات الترفيه
            { id: '107030', name: 'MBC 1 HD', category: 'ترفيه' },
            { id: '107031', name: 'MBC 2 HD', category: 'ترفيه' }
        ];
        
        const SERVER_URL = 'http://maventv.one:80';
        const USERNAME = 'odaitv';
        const PASSWORD = 'Odai2030';
        
        function createStreamUrl(streamId) {
            return `${SERVER_URL}/live/${USERNAME}/${PASSWORD}/${streamId}.ts`;
        }
        
        function createStreamElement(stream) {
            const streamUrl = createStreamUrl(stream.id);
            
            return `
                <div class="stream-test" id="stream-${stream.id}">
                    <div class="stream-info">
                        <strong>${stream.name}</strong> (${stream.category})
                        <br>
                        <span class="stream-url">${streamUrl}</span>
                    </div>
                    <button class="test-btn" onclick="testStream('${stream.id}')">اختبار</button>
                    <div class="status" id="status-${stream.id}" style="display: none;"></div>
                </div>
            `;
        }
        
        function displayStreams() {
            const container = document.getElementById('streams-container');
            container.innerHTML = streams.map(createStreamElement).join('');
        }
        
        async function testStream(streamId) {
            const statusElement = document.getElementById(`status-${streamId}`);
            const streamUrl = createStreamUrl(streamId);
            
            // إظهار حالة الاختبار
            statusElement.style.display = 'block';
            statusElement.className = 'status testing';
            statusElement.textContent = '🔄 جاري الاختبار...';
            
            try {
                // محاولة الوصول للـ stream
                const response = await fetch(streamUrl, {
                    method: 'HEAD',
                    mode: 'no-cors' // لتجنب مشاكل CORS
                });
                
                // في حالة no-cors، لا يمكننا قراءة الـ status
                // لذلك سنعتبر أن الطلب نجح إذا لم يحدث خطأ
                statusElement.className = 'status success';
                statusElement.textContent = '✅ Stream متاح (لا يمكن التحقق من الحالة بسبب CORS)';
                
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = `❌ خطأ: ${error.message}`;
            }
        }
        
        async function testAllStreams() {
            console.log('🚀 بدء اختبار جميع الـ streams...');
            
            for (const stream of streams) {
                await testStream(stream.id);
                // تأخير بسيط بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            console.log('✅ انتهى اختبار جميع الـ streams');
        }
        
        // عرض الـ streams عند تحميل الصفحة
        window.onload = function() {
            displayStreams();
            
            // رسالة ترحيب
            setTimeout(() => {
                alert('🔍 أداة اختبار Stream URLs\n\n' +
                      '📡 السيرفر: maventv.one:80\n' +
                      '👤 المستخدم: odaitv\n' +
                      '🔗 تنسيق Xtream Codes\n\n' +
                      'ملاحظة: بسبب قيود CORS في المتصفح، قد لا تظهر النتائج الدقيقة.\n' +
                      'للاختبار الدقيق، استخدم التطبيق مباشرة.');
            }, 500);
        };
    </script>
</body>
</html>
