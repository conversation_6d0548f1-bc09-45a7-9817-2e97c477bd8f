# تطبيق النسور الماسية للتجارة - نظام إدارة مخزون بطاريات الدواجن

## نظرة عامة
تطبيق Android أصلي لإدارة مخزون بطاريات الدواجن لشركة النسور الماسية للتجارة. يوفر نظاماً متكاملاً لإدارة المنتجات وطلبات العملاء مع واجهة عربية سهلة الاستخدام.

## الميزات الرئيسية

### 📱 واجهة المستخدم
- تصميم عربي متجاوب (RTL)
- واجهة محسنة للهواتف المحمولة
- دعم الوضع الليلي
- أيقونات وألوان احترافية

### 📊 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تصنيف المنتجات (إنتاج/تربية)
- تتبع حالة المنتجات (متاح/محجوز)
- حساب السعة الإجمالية تلقائياً
- البحث والفلترة المتقدمة

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تسجيل طلبات العملاء
- تصنيف العملاء والطلبات
- البحث السريع في بيانات العملاء

### 📄 التقارير والتصدير
- تصدير البيانات بصيغ متعددة (PDF, Word, Excel)
- تقارير تفصيلية للمنتجات والعملاء
- إحصائيات شاملة في لوحة التحكم

### 💾 إدارة البيانات
- حفظ البيانات محلياً على الجهاز
- نظام النسخ الاحتياطي
- استيراد واستعادة البيانات
- حماية البيانات بالتشفير

## متطلبات النظام

### للتطوير
- Android Studio Arctic Fox أو أحدث
- Java Development Kit (JDK) 8 أو أحدث
- Android SDK API Level 21 أو أحدث
- Gradle 8.0 أو أحدث

### للتشغيل
- Android 5.0 (API Level 21) أو أحدث
- 50 MB مساحة تخزين فارغة
- 2 GB RAM (مُوصى به)

## طريقة البناء والتثبيت

### 1. بناء APK
```bash
# في مجلد المشروع
./gradlew assembleDebug

# أو استخدم الملف المساعد
build_apk.bat
```

### 2. تثبيت التطبيق
1. انسخ ملف APK إلى هاتفك الأندرويد
2. فعّل "مصادر غير معروفة" في إعدادات الأمان
3. اضغط على ملف APK لتثبيت التطبيق

## بنية المشروع

```
android_app/
├── app/
│   ├── src/main/
│   │   ├── java/com/diamondeagles/inventory/
│   │   │   ├── MainActivity.java
│   │   │   └── SplashActivity.java
│   │   ├── res/
│   │   │   ├── layout/
│   │   │   ├── values/
│   │   │   ├── drawable/
│   │   │   └── mipmap/
│   │   ├── assets/
│   │   │   ├── index.html
│   │   │   ├── style.css
│   │   │   └── script.js
│   │   └── AndroidManifest.xml
│   └── build.gradle
├── gradle/
├── build.gradle
├── settings.gradle
└── README.md
```

## التقنيات المستخدمة

- **Android WebView**: لعرض واجهة الويب
- **HTML5/CSS3/JavaScript**: للواجهة الأمامية
- **Material Design**: لتصميم الواجهة
- **Local Storage**: لحفظ البيانات محلياً
- **File API**: لتصدير الملفات

## الأذونات المطلوبة

- `INTERNET`: للوصول للإنترنت (للخطوط والمكتبات)
- `ACCESS_NETWORK_STATE`: لفحص حالة الشبكة
- `WRITE_EXTERNAL_STORAGE`: لحفظ الملفات المصدرة
- `READ_EXTERNAL_STORAGE`: لقراءة الملفات

## الدعم والصيانة

### الإصدار الحالي: 1.0
- تاريخ الإصدار: ديسمبر 2023
- متوافق مع Android 5.0+
- يدعم الشاشات من 4" إلى 12"

### التحديثات المستقبلية
- [ ] دعم قاعدة البيانات السحابية
- [ ] مزامنة البيانات بين الأجهزة
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] دعم اللغة الإنجليزية
- [ ] نظام إشعارات متقدم

## الأمان والخصوصية

- جميع البيانات محفوظة محلياً على الجهاز
- لا يتم إرسال أي بيانات لخوادم خارجية
- تشفير البيانات الحساسة
- نسخ احتياطية آمنة

## الترخيص

هذا التطبيق مطور خصيصاً لشركة النسور الماسية للتجارة.
جميع الحقوق محفوظة © 2023

## التواصل والدعم

للدعم الفني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX
- الموقع الإلكتروني: www.diamondeagles.com

---

**ملاحظة**: هذا التطبيق مُحسن للاستخدام على الهواتف الذكية والأجهزة اللوحية التي تعمل بنظام Android.
