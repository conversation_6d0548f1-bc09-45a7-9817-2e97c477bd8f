<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة النسور الماسية للتجارة - نظام إدارة مخزون بطاريات الدواجن</title>

    <!-- Custom Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,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">

    <!-- Fonts & Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- html2pdf Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

    <!-- Firebase SDK for Web -->
    <script type="module" src="firebase-config.js"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Login Check Script -->
    <script>
        // Check if user is logged in
        function checkLoginStatus() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const loginTime = localStorage.getItem('loginTime');

            if (isLoggedIn !== 'true') {
                // Not logged in, redirect to login page
                window.location.href = 'login-system.html';
                return false;
            }

            if (loginTime) {
                const loginDate = new Date(loginTime);
                const now = new Date();
                const hoursDiff = (now - loginDate) / (1000 * 60 * 60);

                if (hoursDiff >= 24) {
                    // Login expired (24 hours), clear session and redirect
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('loginTime');
                    window.location.href = 'login-system.html';
                    return false;
                }
            }

            return true;
        }

        // Check if in browse mode
        function isBrowseMode() {
            return localStorage.getItem('isBrowseMode') === 'true';
        }

        // Apply browse mode restrictions
        function applyBrowseModeRestrictions() {
            if (isBrowseMode()) {
                console.log('🔍 تطبيق قيود وضع التصفح...');

                // Hide all sections except products
                const sections = ['dashboard', 'customers', 'settings'];
                sections.forEach(sectionId => {
                    const section = document.getElementById(sectionId);
                    if (section) {
                        section.style.display = 'none';
                    }
                });

                // Hide navigation items except products
                const navItems = document.querySelectorAll('.nav-link');
                navItems.forEach(item => {
                    if (!item.classList.contains('nav-products')) {
                        item.style.display = 'none';
                    }
                });

                // Show only products section
                const productsSection = document.getElementById('products');
                if (productsSection) {
                    productsSection.classList.add('active');
                }

                // Update header to show browse mode
                const logoText = document.querySelector('.logo-text h1');
                if (logoText) {
                    logoText.innerHTML = 'شركة النسور الماسية للتجارة <span style="font-size: 0.7em; color: #28a745;">(وضع التصفح)</span>';
                }

                // Update exit button
                const exitBtn = document.getElementById('exitAppBtn');
                if (exitBtn) {
                    exitBtn.innerHTML = '<i class="fas fa-times"></i><span>إنهاء التصفح</span>';
                    exitBtn.onclick = exitBrowseMode;
                }

                // Hide add product button and other action buttons
                setTimeout(() => {
                    const addButtons = document.querySelectorAll('.btn-primary, .btn-danger, .btn-warning');
                    addButtons.forEach(btn => {
                        if (btn.textContent.includes('إضافة') || btn.textContent.includes('حذف') || btn.textContent.includes('تصدير')) {
                            btn.style.display = 'none';
                        }
                    });

                    // Hide products actions section
                    const productsActions = document.querySelector('.products-actions');
                    if (productsActions) {
                        productsActions.style.display = 'none';
                    }

                    // Add browse mode notice
                    const sectionHeader = document.querySelector('#products .section-header');
                    if (sectionHeader && !document.getElementById('browseNotice')) {
                        const notice = document.createElement('div');
                        notice.id = 'browseNotice';
                        notice.innerHTML = `
                            <div style="background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                                <i class="fas fa-eye" style="margin-left: 10px;"></i>
                                <strong>وضع التصفح:</strong> يمكنك عرض المنتجات فقط. للحصول على صلاحيات كاملة، يرجى تسجيل الدخول كمدير.
                            </div>
                        `;
                        sectionHeader.appendChild(notice);
                    }
                }, 1000);
            }
        }

        // Exit browse mode
        function exitBrowseMode() {
            localStorage.removeItem('isBrowseMode');
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('loginTime');
            window.location.href = 'login-system.html';
        }

        // Check login status immediately
        if (!checkLoginStatus()) {
            // Stop loading the page if not logged in
            document.documentElement.style.display = 'none';
        } else {
            // Apply browse mode restrictions if needed
            document.addEventListener('DOMContentLoaded', function() {
                applyBrowseModeRestrictions();

                // Auto-load data from Google Drive when page loads
                setTimeout(async () => {
                    console.log('🔄 محاولة تحميل البيانات من Google Drive...');

                    // Try to initialize Google Drive and download data
                    if (typeof initializeGoogleDrive === 'function') {
                        try {
                            const initialized = await initializeGoogleDrive();
                            if (initialized && window.isGoogleDriveConnected) {
                                console.log('📥 تحميل البيانات المحفوظة...');

                                // Download products if function exists
                                if (typeof downloadProductsFromDrive === 'function') {
                                    await downloadProductsFromDrive();
                                }

                                // Download customers if function exists
                                if (typeof downloadCustomersFromDrive === 'function') {
                                    await downloadCustomersFromDrive();
                                }

                                console.log('✅ تم تحميل البيانات بنجاح');
                            } else {
                                console.log('ℹ️ Google Drive غير متصل - استخدام البيانات المحلية');
                            }
                        } catch (error) {
                            console.log('⚠️ فشل في تحميل البيانات من Google Drive:', error);
                        }
                    }
                }, 3000); // انتظار 3 ثوان لتحميل جميع الملفات
            });
        }
    </script>


    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <svg class="custom-logo-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <!-- Diamond Background -->
                            <defs>
                                <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="eagleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#ffb347;stop-opacity:1" />
                                </linearGradient>
                            </defs>

                            <!-- Diamond Shape -->
                            <polygon points="50,5 85,35 50,65 15,35" fill="url(#diamondGradient)" stroke="#fff" stroke-width="2"/>

                            <!-- Eagle Silhouette -->
                            <path d="M50 20 C45 18, 40 20, 38 25 C36 30, 40 35, 45 37 L50 40 L55 37 C60 35, 64 30, 62 25 C60 20, 55 18, 50 20 Z" fill="url(#eagleGradient)"/>
                            <circle cx="45" cy="25" r="2" fill="#333"/>
                            <path d="M42 28 L48 30" stroke="#333" stroke-width="1"/>

                            <!-- Poultry Battery Representation -->
                            <rect x="25" y="70" width="50" height="25" rx="3" fill="#4a90e2" stroke="#fff" stroke-width="1"/>
                            <rect x="30" y="75" width="8" height="6" fill="#fff" opacity="0.8"/>
                            <rect x="42" y="75" width="8" height="6" fill="#fff" opacity="0.8"/>
                            <rect x="54" y="75" width="8" height="6" fill="#fff" opacity="0.8"/>
                            <rect x="66" y="75" width="8" height="6" fill="#fff" opacity="0.8"/>

                            <!-- Small decorative elements -->
                            <circle cx="20" cy="15" r="2" fill="#ffd700" opacity="0.6"/>
                            <circle cx="80" cy="15" r="2" fill="#ffd700" opacity="0.6"/>
                            <circle cx="15" cy="50" r="1.5" fill="#ffd700" opacity="0.4"/>
                            <circle cx="85" cy="50" r="1.5" fill="#ffd700" opacity="0.4"/>
                        </svg>
                    </div>
                    <div class="logo-text">
                        <h1>شركة النسور الماسية للتجارة</h1>
                        <p>نظام إدارة مخزون بطاريات الدواجن</p>
                    </div>
                </div>

                <!-- Exit Button -->
                <div class="header-actions">
                    <button id="exitAppBtn" class="exit-app-btn" onclick="logoutUser()" title="تسجيل خروج من التطبيق">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل خروج</span>
                    </button>
                </div>
                <nav class="main-nav">
                    <ul class="nav-menu">
                        <li><a href="#" onclick="showSection('dashboard')" class="nav-link nav-dashboard active">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a></li>
                        <li><a href="#" onclick="showSection('products')" class="nav-link nav-products">
                            <i class="fas fa-boxes"></i>
                            <span>إدارة المنتجات</span>
                        </a></li>
                        <li><a href="#" onclick="showSection('customers')" class="nav-link nav-customers">
                            <i class="fas fa-users"></i>
                            <span>طلبات العملاء</span>
                        </a></li>
                        <li><a href="#" onclick="showSection('settings')" class="nav-link nav-settings">
                            <i class="fas fa-cogs"></i>
                            <span>الإعدادات</span>
                        </a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Dashboard Section -->
            <section id="dashboard" class="section active">
                <div class="dashboard-header">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم الرئيسية</h2>
                </div>
                
                <div class="stats-grid">
                    <!-- Products Management Card - Modern Design -->
                    <div class="products-management-card" onclick="navigateToProducts()" title="انقر للانتقال إلى إدارة المنتجات">
                        <div class="card-header">
                            <div class="header-icon">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div class="header-content">
                                <h3>إدارة المنتجات</h3>
                                <p>نظام شامل لإدارة المخزون</p>
                            </div>
                        </div>

                        <div class="card-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="totalProducts">٠</div>
                                <div class="stat-label">إجمالي المنتجات</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="availableProducts">٠</div>
                                <div class="stat-label">متاح</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="reservedProducts">٠</div>
                                <div class="stat-label">محجوز</div>
                            </div>
                        </div>

                        <div class="card-actions">
                            <div class="action-item">
                                <i class="fas fa-plus-circle"></i>
                                <span>إضافة منتج جديد</span>
                            </div>
                            <div class="action-item">
                                <i class="fas fa-search"></i>
                                <span>البحث والفلترة</span>
                            </div>
                            <div class="action-item">
                                <i class="fas fa-download"></i>
                                <span>تصدير التقارير</span>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="capacity-info">
                                <i class="fas fa-chart-bar"></i>
                                <span>السعة الإجمالية: <strong id="totalCapacity">٠</strong> طائر</span>
                            </div>
                            <div class="navigate-arrow">
                                <i class="fas fa-arrow-left"></i>
                            </div>
                        </div>

                        <div class="card-decoration">
                            <i class="fas fa-warehouse"></i>
                        </div>
                    </div>

                    <!-- Total Customers Card - Purple Theme -->
                    <div class="stat-card stat-card-purple clickable" onclick="navigateToCustomers()" title="انقر للانتقال إلى طلبات العملاء">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalCustomers">٠</h3>
                            <p>إجمالي العملاء</p>
                        </div>
                        <div class="click-indicator">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="stat-decoration">
                            <i class="fas fa-users decoration-icon"></i>
                        </div>
                    </div>



                    <!-- Breeding Requests Card - Teal Theme -->
                    <div class="stat-card stat-card-teal clickable" onclick="navigateToCustomers()" title="انقر للانتقال إلى طلبات العملاء">
                        <div class="stat-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="breedingRequests">٠</h3>
                            <p>تربية</p>
                        </div>
                        <div class="click-indicator">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="stat-decoration">
                            <i class="fas fa-seedling decoration-icon"></i>
                        </div>
                    </div>

                    <!-- Production Requests Card - Pink Theme -->
                    <div class="stat-card stat-card-pink clickable" onclick="navigateToCustomers()" title="انقر للانتقال إلى طلبات العملاء">
                        <div class="stat-icon">
                            <i class="fas fa-egg"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="productionRequests">٠</h3>
                            <p>إنتاج</p>
                        </div>
                        <div class="click-indicator">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="stat-decoration">
                            <i class="fas fa-egg decoration-icon"></i>
                        </div>
                    </div>
                </div>

                <div class="welcome-section">
                    <div class="welcome-card">
                        <div class="welcome-header">
                            <div class="welcome-icon">
                                <svg class="welcome-dashboard-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                    <defs>
                                        <linearGradient id="welcomeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
                                            <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:0.7" />
                                        </linearGradient>
                                    </defs>
                                    <!-- Dashboard representation -->
                                    <rect x="10" y="20" width="80" height="60" rx="8" fill="url(#welcomeGradient)" stroke="rgba(255,255,255,0.5)" stroke-width="2"/>
                                    <!-- Chart bars -->
                                    <rect x="20" y="50" width="8" height="20" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="35" y="40" width="8" height="30" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="50" y="45" width="8" height="25" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="65" y="35" width="8" height="35" fill="rgba(255,255,255,0.8)"/>
                                    <!-- Decorative elements -->
                                    <circle cx="25" cy="30" r="3" fill="rgba(255,255,255,0.6)"/>
                                    <circle cx="75" cy="30" r="3" fill="rgba(255,255,255,0.6)"/>
                                </svg>
                            </div>
                            <div class="welcome-content">
                                <h3>مرحباً بك في نظام إدارة المخزون</h3>
                                <p>نظام متكامل لإدارة منتجات بطاريات الدواجن وطلبات العملاء بكل سهولة واحترافية</p>
                            </div>
                        </div>

                        <div class="quick-actions">
                            <button class="btn btn-gradient-blue" onclick="showSection('products')">
                                <i class="fas fa-plus"></i>
                                <span>إضافة منتج جديد</span>
                                <div class="btn-shine"></div>
                            </button>
                            <button class="btn btn-gradient-purple" onclick="showSection('customers')">
                                <i class="fas fa-user-plus"></i>
                                <span>إضافة عميل جديد</span>
                                <div class="btn-shine"></div>
                            </button>
                            <button class="btn btn-gradient-green" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i>
                                <span>إعدادات النظام</span>
                                <div class="btn-shine"></div>
                            </button>
                        </div>

                        <div class="system-features">
                            <div class="feature-item">
                                <i class="fas fa-chart-line"></i>
                                <span>تقارير تفصيلية</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-mobile-alt"></i>
                                <span>واجهة متجاوبة</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>حفظ آمن للبيانات</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-download"></i>
                                <span>تصدير متعدد الصيغ</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Products Section -->
            <section id="products" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-boxes"></i> إدارة المنتجات</h2>
                    <button class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="fas fa-plus"></i>
                        إضافة منتج جديد
                    </button>
                </div>

                <div class="section-content">
                    <div class="filters-section">
                        <div class="search-and-filters">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="productSearch" placeholder="البحث في المنتجات..." onkeyup="searchProducts()">
                            </div>

                            <div class="filters-grid">
                                <select id="productStatusFilter" onchange="searchProducts()">
                                    <option value="">جميع الحالات</option>
                                    <option value="متاح">متاح</option>
                                    <option value="محجوز">محجوز</option>
                                </select>

                                <select id="productCategoryFilter" onchange="searchProducts()">
                                    <option value="">جميع الفئات</option>
                                    <option value="انتاج">انتاج</option>
                                    <option value="تربية">تربية</option>
                                </select>

                                <button class="btn btn-secondary" onclick="clearProductFilters()">
                                    <i class="fas fa-times"></i>
                                    مسح الفلاتر
                                </button>

                                <button class="btn btn-success" onclick="showFilteredExportOptions()">
                                    <i class="fas fa-download"></i>
                                    تصدير النتائج
                                </button>
                            </div>
                        </div>
                    </div>

                <!-- Products Actions -->
                <div class="products-actions">
                    <div class="export-buttons">
                        <button class="btn btn-success" onclick="exportAllProducts('word')">
                            <i class="fas fa-file-word"></i>
                            تصدير Word
                        </button>
                        <button class="btn btn-danger" onclick="exportAllProducts('pdf')">
                            <i class="fas fa-file-pdf"></i>
                            تصدير PDF
                        </button>
                        <button class="btn btn-info" onclick="showProductsManagement()">
                            <i class="fas fa-cogs"></i>
                            إدارة المنتجات
                        </button>
                        <button class="btn btn-warning" onclick="showFloatingCalculator()" title="الآلة الحاسبة البسيطة">
                            <i class="fas fa-calculator"></i>
                            آلة حاسبة
                        </button>
                        <button class="btn btn-danger btn-delete-all" onclick="confirmDeleteAllProducts()" title="حذف جميع المنتجات">
                            <i class="fas fa-trash-alt"></i>
                            حذف الكل
                        </button>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="table-container">
                    <table class="data-table" id="productsTable">
                        <thead>
                            <tr>
                                <th>الشركة المصنعة</th>
                                <th>بلد المنشأ</th>
                                <th>الفئة</th>
                                <th>الحالة</th>
                                <th>تاريخ الفك</th>
                                <th>الكمية المتاحة</th>
                                <th>عدد الأدوار</th>
                                <th>عدد الخطوط</th>
                                <th>عدد العشوش/خط</th>
                                <th>مقاس العش (سم)</th>
                                <th>إجمالي عدد العشوش</th>
                                <th>السعة الإجمالية</th>
                                <th>السعر</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <!-- Products will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Customers Section -->
            <section id="customers" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> إدارة طلبات العملاء</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="showAddCustomerModal()">
                            <i class="fas fa-user-plus"></i>
                            إضافة عميل جديد
                        </button>
                        <button class="btn btn-danger btn-delete-all" onclick="confirmDeleteAllCustomers()" title="حذف جميع العملاء">
                            <i class="fas fa-trash-alt"></i>
                            حذف الكل
                        </button>
                    </div>
                </div>

                <!-- Customers Filters -->
                <div class="filters-section">
                    <div class="filters-header">
                        <div class="search-and-filters">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="customerSearch" placeholder="البحث برقم العميل أو الاسم أو رقم الجوال...">
                            </div>

                            <div class="filters-grid">
                                <select id="customerTypeFilter">
                                    <option value="">جميع أنواع العملاء</option>
                                    <option value="عميل عادي">عميل عادي</option>
                                    <option value="عميل محتمل">عميل محتمل</option>
                                </select>

                                <select id="requestTypeFilter">
                                    <option value="">جميع أنواع الطلبات</option>
                                    <option value="تربية">تربية</option>
                                    <option value="انتاج">انتاج</option>
                                </select>

                                <select id="nationalityFilter">
                                    <option value="">جميع الجنسيات</option>
                                    <!-- Will be populated dynamically -->
                                </select>

                                <button class="btn btn-secondary" onclick="clearCustomerFilters()">
                                    <i class="fas fa-times"></i>
                                    مسح الفلاتر
                                </button>
                            </div>
                        </div>

                        <div class="export-actions">
                            <button class="btn btn-success" onclick="exportFilteredCustomers('word')">
                                <i class="fas fa-file-word"></i>
                                تصدير Word
                            </button>
                            <button class="btn btn-info" onclick="exportFilteredCustomers('excel')">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                            <button class="btn btn-danger" onclick="exportFilteredCustomers('pdf')">
                                <i class="fas fa-file-pdf"></i>
                                تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="table-container">
                    <table class="data-table" id="customersTable">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>رقم الجوال</th>
                                <th>الجنسية</th>
                                <th>نوع العميل</th>
                                <th>نوع الطلب</th>
                                <th>تفاصيل الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <!-- Customers will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-cogs"></i> إعدادات النظام</h2>
                </div>

                <div class="settings-container">
                    <!-- Company Information -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-building"></i> بيانات الشركة</h3>
                        </div>
                        <div class="settings-card-body">
                            <form id="companySettingsForm">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="companyName">اسم الشركة</label>
                                        <input type="text" id="companyName" name="companyName" value="شركة النسور الماسية للتجارة">
                                    </div>
                                    <div class="form-group">
                                        <label for="commercialRegister">السجل التجاري</label>
                                        <input type="text" id="commercialRegister" name="commercialRegister" placeholder="رقم السجل التجاري">
                                    </div>
                                    <div class="form-group">
                                        <label for="taxNumber">الرقم الضريبي</label>
                                        <input type="text" id="taxNumber" name="taxNumber" placeholder="الرقم الضريبي (اختياري)">
                                    </div>
                                    <div class="form-group">
                                        <label for="companyCountry">الدولة</label>
                                        <select id="companyCountry" name="companyCountry">
                                            <option value="">اختر الدولة</option>
                                            <option value="السعودية">السعودية</option>
                                            <option value="الإمارات">الإمارات العربية المتحدة</option>
                                            <option value="الكويت">الكويت</option>
                                            <option value="قطر">قطر</option>
                                            <option value="البحرين">البحرين</option>
                                            <option value="عمان">عمان</option>
                                            <option value="مصر">مصر</option>
                                            <option value="الأردن">الأردن</option>
                                            <option value="لبنان">لبنان</option>
                                            <option value="سوريا">سوريا</option>
                                            <option value="العراق">العراق</option>
                                            <option value="فلسطين">فلسطين</option>
                                            <option value="المغرب">المغرب</option>
                                            <option value="الجزائر">الجزائر</option>
                                            <option value="تونس">تونس</option>
                                            <option value="ليبيا">ليبيا</option>
                                            <option value="السودان">السودان</option>
                                            <option value="اليمن">اليمن</option>
                                        </select>
                                    </div>
                                    <div class="form-group full-width">
                                        <label for="companyAddress">العنوان</label>
                                        <textarea id="companyAddress" name="companyAddress" rows="3" placeholder="العنوان التفصيلي للشركة"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="companyPhone">رقم الجوال الرئيسي</label>
                                        <input type="tel" id="companyPhone" name="companyPhone" value="">
                                    </div>
                                    <div class="form-group">
                                        <label for="whatsappNumber">رقم واتساب</label>
                                        <input type="tel" id="whatsappNumber" name="whatsappNumber" value="">
                                    </div>
                                </div>

                                <!-- Additional Contact Numbers Section -->
                                <div class="contact-numbers-section">
                                    <h4><i class="fas fa-phone-alt"></i> أرقام التواصل الإضافية</h4>
                                    <div id="additionalContacts">
                                        <!-- Additional contacts will be added here -->
                                    </div>
                                    <button type="button" class="btn btn-secondary btn-sm" onclick="addContactNumber()">
                                        <i class="fas fa-plus"></i> إضافة رقم تواصل
                                    </button>
                                </div>

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="supportEmail">بريد الدعم</label>
                                        <input type="email" id="supportEmail" name="supportEmail" placeholder="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label for="companyWebsite">الموقع الإلكتروني</label>
                                        <input type="url" id="companyWebsite" name="companyWebsite" placeholder="https://www.company.com">
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ بيانات الشركة
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- User Management Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-user-cog"></i> إدارة المستخدمين</h3>
                        </div>
                        <div class="settings-card-body">
                            <form id="userManagementForm">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="adminEmail">البريد الإلكتروني للمدير</label>
                                        <input type="email" id="adminEmail" name="adminEmail" value="<EMAIL>" placeholder="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label for="adminPassword">كلمة مرور المدير</label>
                                        <input type="password" id="adminPassword" name="adminPassword" value="2030" placeholder="كلمة المرور">
                                    </div>
                                </div>

                                <div class="user-info" style="margin: 15px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff;">
                                    <p style="margin: 0; font-size: 14px;">
                                        <i class="fas fa-info-circle" style="color: #007bff;"></i>
                                        <strong>المستخدم الحالي:</strong> <span id="currentUserDisplay"><EMAIL></span>
                                    </p>
                                    <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                                        <i class="fas fa-clock"></i> آخر تسجيل دخول: <span id="lastLoginDisplay">غير محدد</span>
                                    </p>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ بيانات المستخدم
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="logoutUser()">
                                        <i class="fas fa-sign-out-alt"></i> تسجيل خروج
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Financial Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-calculator"></i> الإعدادات المالية</h3>
                        </div>
                        <div class="settings-card-body">
                            <form id="financialSettingsForm">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="taxRate">نسبة الضريبة (%)</label>
                                        <input type="number" id="taxRate" name="taxRate" min="0" max="100" step="0.01" value="15" placeholder="15.00">
                                    </div>
                                    <div class="form-group">
                                        <label for="appLanguage">لغة التطبيق</label>
                                        <select id="appLanguage" name="appLanguage" onchange="changeAppLanguage(this.value)">
                                            <option value="ar">العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> حفظ الإعدادات المالية
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Documents Management -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-file-upload"></i> إدارة الوثائق</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="documents-section">
                                <div class="upload-area" onclick="document.getElementById('documentUpload').click()">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>انقر لرفع الوثائق</p>
                                    <small>PDF, DOC, DOCX, JPG, PNG (حد أقصى 10MB)</small>
                                </div>
                                <input type="file" id="documentUpload" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;" onchange="handleDocumentUpload(event)">

                                <div class="documents-list" id="documentsList">
                                    <!-- Documents will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- Cloud Storage System -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-cloud"></i> نظام التخزين السحابي</h3>
                            <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">جميع البيانات محفوظة في السحابة تلقائياً</p>
                        </div>
                        <div class="settings-card-body">
                            <div class="cloud-info">
                                <div class="info-item">
                                    <i class="fas fa-wifi"></i>
                                    <span>حالة الاتصال:</span>
                                    <div id="googleDriveStatus" class="connection-status-inline">
                                        <span class="status-text offline">غير متصل</span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-user"></i>
                                    <span>المستخدم: <span id="googleDriveUser">غير متصل</span></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-clock"></i>
                                    <span>آخر مزامنة: <span id="lastGoogleDriveSync">لم يتم بعد</span></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-folder"></i>
                                    <span>مجلد البيانات: النسور الماسية</span>
                                </div>
                            </div>

                            <!-- Google Drive Actions -->
                            <div class="cloud-actions">
                                <button type="button" class="btn btn-primary" onclick="connectToGoogleDrive()" id="googleDriveConnectBtn">
                                    <i class="fab fa-google"></i> تسجيل دخول Google
                                </button>
                                <button type="button" class="btn btn-success cloud-btn sync-btn" onclick="uploadAllToGoogleDrive()" data-original-text="رفع جميع البيانات" disabled id="googleDriveUploadBtn">
                                    <i class="fas fa-cloud-upload-alt"></i> رفع جميع البيانات
                                </button>
                                <button type="button" class="btn btn-info cloud-btn sync-btn" onclick="downloadAllFromGoogleDrive()" data-original-text="تحميل جميع البيانات" disabled id="googleDriveDownloadBtn">
                                    <i class="fas fa-cloud-download-alt"></i> تحميل جميع البيانات
                                </button>
                                <button type="button" class="btn btn-warning" onclick="checkGoogleDriveConnection()" id="googleDriveCheckBtn">
                                    <i class="fas fa-wifi"></i> فحص الاتصال
                                </button>
                            </div>

                            <!-- Auto Sync Settings -->
                            <div class="auto-sync-toggle">
                                <input type="checkbox" id="autoSyncEnabled" checked onchange="toggleAutoSync()">
                                <label for="autoSyncEnabled">تفعيل المزامنة التلقائية (كل 30 ثانية)</label>
                            </div>

                            <div class="cloud-actions" style="margin-top: 10px;">
                                <button type="button" class="btn btn-info" onclick="performManualSync()">
                                    <i class="fas fa-sync-alt"></i> مزامنة يدوية فورية
                                </button>
                            </div>

                            <div class="cloud-info-note" style="margin-top: 15px; padding: 10px; background-color: #e8f5e8; border-radius: 5px; border-left: 4px solid #28a745;">
                                <p style="margin: 0; font-size: 14px;">
                                    <i class="fas fa-cloud-upload-alt" style="color: #28a745;"></i>
                                    <strong>التخزين السحابي الذكي:</strong> جميع البيانات محفوظة تلقائياً في Google Drive
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                                    <i class="fas fa-sync-alt"></i> مزامنة فورية: البيانات متاحة لجميع المستخدمين فوراً
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                                    <i class="fas fa-shield-alt"></i> آمان عالي: نسخ احتياطية تلقائية ومشفرة
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                                    <i class="fas fa-users"></i> عمل جماعي: مشاركة البيانات مع الفريق بسهولة
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                                    <i class="fas fa-globe"></i> وصول عالمي: من أي مكان وأي جهاز
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Logo Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-image"></i> شعار الشركة</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="logo-section">
                                <div class="current-logo" id="currentLogo">
                                    <div class="logo-placeholder">
                                        <i class="fas fa-building"></i>
                                        <p>لا يوجد شعار محدد</p>
                                    </div>
                                </div>
                                <div class="logo-upload">
                                    <button type="button" class="btn btn-primary" onclick="document.getElementById('logoUpload').click()">
                                        <i class="fas fa-upload"></i> رفع شعار جديد
                                    </button>
                                    <input type="file" id="logoUpload" accept="image/*" style="display: none;" onchange="handleLogoUpload(event)">
                                    <button type="button" class="btn btn-danger" onclick="removeLogo()" id="removeLogoBtn" style="display: none;">
                                        <i class="fas fa-trash"></i> حذف الشعار
                                    </button>
                                </div>
                                <small class="upload-note">يُفضل استخدام صور بصيغة PNG أو JPG بحجم 200x200 بكسل</small>
                            </div>
                        </div>
                    </div>

                    <!-- Date Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-calendar-alt"></i> إعدادات التاريخ</h3>
                        </div>
                        <div class="settings-card-body">
                            <form id="dateSettingsForm">
                                <div class="form-group">
                                    <label for="dateFormat">نوع التقويم المستخدم</label>
                                    <select id="dateFormat" name="dateFormat">
                                        <option value="gregorian" selected>التقويم الميلادي</option>
                                        <option value="hijri">التقويم الهجري</option>
                                    </select>
                                </div>
                                <div class="date-preview">
                                    <p><strong>معاينة التاريخ الحالي:</strong></p>
                                    <div id="datePreview" class="date-display">
                                        <!-- Date preview will be shown here -->
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ إعدادات التاريخ
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Theme Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-palette"></i> مظهر الألوان</h3>
                        </div>
                        <div class="settings-card-body">
                            <form id="themeSettingsForm">
                                <div class="form-group">
                                    <label for="colorTheme">اختر مظهر الألوان</label>
                                    <select id="colorTheme" name="colorTheme">
                                        <option value="default">المظهر الافتراضي (أزرق-بنفسجي)</option>
                                        <option value="ocean">مظهر المحيط (أزرق-أخضر)</option>
                                        <option value="sunset">مظهر الغروب (برتقالي-وردي)</option>
                                        <option value="forest">مظهر الغابة (أخضر-أزرق داكن)</option>
                                        <option value="royal">مظهر ملكي (بنفسجي-ذهبي)</option>
                                        <option value="dark">المظهر الداكن (رمادي-أسود)</option>
                                        <option value="professional">المظهر المهني (أزرق-رمادي)</option>
                                    </select>
                                </div>
                                <div class="theme-preview" id="themePreview">
                                    <div class="preview-card">
                                        <div class="preview-header">معاينة المظهر</div>
                                        <div class="preview-stats">
                                            <div class="preview-stat">
                                                <div class="preview-icon"></div>
                                                <div class="preview-content">
                                                    <h4>123</h4>
                                                    <p>إجمالي المنتجات</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> تطبيق المظهر
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetTheme()">
                                        <i class="fas fa-undo"></i> إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Developer Tools -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-code"></i> أدوات المطورين</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="developer-actions">
                                <button type="button" class="btn btn-info" onclick="testProductSpecifications()">
                                    <i class="fas fa-search"></i> فحص مواصفات المنتجات
                                </button>
                                <button type="button" class="btn btn-success" onclick="addSampleSpecificationsToProducts()">
                                    <i class="fas fa-plus"></i> إضافة مواصفات تجريبية
                                </button>
                                <button type="button" class="btn btn-warning" onclick="testLogoInExport()">
                                    <i class="fas fa-image"></i> اختبار الشعار في التصدير
                                </button>
                                <button type="button" class="btn btn-primary" onclick="testProductFilters()">
                                    <i class="fas fa-filter"></i> اختبار فلاتر المنتجات
                                </button>
                                <button type="button" class="btn btn-warning" onclick="testTotalCapacityCalculation()">
                                    <i class="fas fa-calculator"></i> اختبار حساب السعة الإجمالية
                                </button>

                                <small class="developer-note">
                                    <i class="fas fa-info-circle"></i>
                                    هذه الأدوات مخصصة لاختبار وتشخيص المشاكل. تحقق من وحدة التحكم (F12) لرؤية النتائج.
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- System Actions -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-tools"></i> إجراءات النظام</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="system-actions">
                                <button type="button" class="btn btn-info" onclick="exportAllData()">
                                    <i class="fas fa-download"></i> تصدير جميع البيانات
                                </button>
                                <button type="button" class="btn btn-warning" onclick="clearCache()">
                                    <i class="fas fa-broom"></i> مسح ذاكرة التخزين المؤقت
                                </button>
                                <button type="button" class="btn btn-info" onclick="showSplashScreenNow()">
                                    <i class="fas fa-play"></i> عرض الشاشة الافتتاحية
                                </button>
                                <button type="button" class="btn btn-danger" onclick="resetAllSettings()">
                                    <i class="fas fa-undo"></i> إعادة تعيين جميع الإعدادات
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- License Management -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-key"></i> إدارة التراخيص</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="license-management">
                                <button type="button" class="btn btn-info" onclick="generateActivationCodes()">
                                    <i class="fas fa-list"></i> عرض أكواد التفعيل
                                </button>
                                <button type="button" class="btn btn-success" onclick="showLicenseScreen()">
                                    <i class="fas fa-shield-alt"></i> شاشة التفعيل
                                </button>
                                <button type="button" class="btn btn-warning" onclick="resetLicense()">
                                    <i class="fas fa-redo"></i> إعادة تعيين الترخيص
                                </button>
                                <button type="button" class="btn btn-primary" onclick="console.log(LICENSE_SYSTEM.checkLicense())">
                                    <i class="fas fa-check"></i> فحص الترخيص
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="calculateBarnLengthManual()">
                                    <i class="fas fa-ruler"></i> اختبار حساب طول العنبر
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="calculateBarnWidthManual()">
                                    <i class="fas fa-arrows-alt-h"></i> اختبار حساب عرض العنبر
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="calculateBarnHeightManual()">
                                    <i class="fas fa-arrows-alt-v"></i> اختبار حساب ارتفاع العنبر
                                </button>
                                <button type="button" class="btn btn-success" onclick="testBarnDimensionsCalculation()">
                                    <i class="fas fa-cube"></i> اختبار شامل لأبعاد العنبر
                                </button>
                                <button type="button" class="btn btn-warning" onclick="checkDismantleNotifications()">
                                    <i class="fas fa-bell"></i> فحص إشعارات الفك
                                </button>
                                <button type="button" class="btn btn-info" onclick="testDismantleNotifications()">
                                    <i class="fas fa-vial"></i> اختبار إشعارات الفك
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="addSampleDismantleDates()">
                                    <i class="fas fa-calendar-plus"></i> إضافة تواريخ فك تجريبية
                                </button>
                                <button type="button" class="btn btn-primary" onclick="testDismantleDateValidation()">
                                    <i class="fas fa-calendar-check"></i> اختبار التحقق من تاريخ الفك
                                </button>
                                <button type="button" class="btn btn-info" onclick="testGregorianDateFormatting()">
                                    <i class="fas fa-calendar-alt"></i> اختبار التواريخ الميلادية
                                </button>
                                <button type="button" class="btn btn-warning" onclick="resetWelcomeNotification()">
                                    <i class="fas fa-undo"></i> إعادة تعيين إشعار الترحيب
                                </button>
                                <button type="button" class="btn btn-success" onclick="showWelcomeNotificationManual()">
                                    <i class="fas fa-hand-wave"></i> عرض إشعار الترحيب
                                </button>
                                <button type="button" class="btn btn-primary" onclick="testLanguageChangeNotification()">
                                    <i class="fas fa-language"></i> اختبار تغيير اللغة
                                </button>
                                <button type="button" class="btn btn-warning" onclick="testCleanupNotifications()">
                                    <i class="fas fa-broom"></i> اختبار تنظيف الإشعارات
                                </button>
                                <button type="button" class="btn btn-danger" onclick="forceCleanupAllNotifications()">
                                    <i class="fas fa-trash-alt"></i> إزالة جميع الإشعارات
                                </button>
                                <button type="button" class="btn btn-success" onclick="testCurrencyPriceFields()">
                                    <i class="fas fa-coins"></i> اختبار حقول العملة والسعر
                                </button>
                                <button type="button" class="btn btn-warning" onclick="forceEnableCurrencyPrice()">
                                    <i class="fas fa-unlock"></i> تفعيل طارئ للعملة والسعر
                                </button>
                                <button type="button" class="btn btn-info" onclick="testFormValidation()">
                                    <i class="fas fa-check-circle"></i> اختبار صحة النموذج
                                </button>
                                <button type="button" class="btn btn-success" onclick="testAutomaticCalculations()">
                                    <i class="fas fa-calculator"></i> تنفيذ الحسابات التلقائية
                                </button>
                                <button type="button" class="btn btn-info" onclick="testDecimalBarnDimensions()">
                                    <i class="fas fa-decimal"></i> اختبار الأرقام العشرية
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="testReadonlyCalculatedFields()">
                                    <i class="fas fa-lock"></i> اختبار الحقول المحسوبة
                                </button>
                                <button type="button" class="btn btn-warning" onclick="testValidationAttributesRemoval()">
                                    <i class="fas fa-shield-alt"></i> اختبار إزالة خصائص التحقق
                                </button>
                                <button type="button" class="btn btn-dark" onclick="testEditBarnDimensionsReadonly()">
                                    <i class="fas fa-edit"></i> اختبار أبعاد العنبر في التعديل
                                </button>
                                <button type="button" class="btn btn-success" onclick="showBackupManager()">
                                    <i class="fas fa-archive"></i> إدارة النسخ الاحتياطية
                                </button>
                            </div>
                            <div class="license-info">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    أدوات إدارة وتشخيص نظام التراخيص والتفعيل
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Fixed Footer -->
    <footer class="fixed-footer">
        <div class="copyright-text">
            جميع الحقوق محفوظة لمطور التطبيق <strong>كريم واصل</strong> /
            <span class="phone-numbers">01022225982 - **********</span>
            لصالح <strong>شركة النسور الماسية للتجارة</strong> 2025
        </div>
    </footer>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <!-- License Activation Screen -->
    <div id="licenseScreen" class="license-screen" style="display: none;">
        <div class="license-container">
            <div class="license-header">
                <div class="license-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1>تفعيل التطبيق</h1>
                <p>نظام إدارة مخزون بطاريات الدواجن</p>
                <p class="company-name">شركة النسور الماسية للتجارة</p>
            </div>

            <div class="license-form">
                <div class="activation-section">
                    <h3><i class="fas fa-key"></i> إدخال كود التفعيل</h3>
                    <div class="code-input-container">
                        <input type="text" id="activationCode" placeholder="أدخل كود التفعيل (14 رقم)" maxlength="14" class="activation-input">
                        <button onclick="activateApp()" class="activate-btn">
                            <i class="fas fa-unlock"></i>
                            تفعيل
                        </button>
                    </div>
                    <div class="code-info">
                        <p><i class="fas fa-info-circle"></i> كود التفعيل مكون من 14 رقم</p>
                        <p><i class="fas fa-calendar-alt"></i> الاشتراك السنوي أو مدى الحياة</p>
                    </div>
                </div>

                <div class="license-status" id="licenseStatus" style="display: none;">
                    <h3><i class="fas fa-check-circle"></i> حالة الترخيص</h3>
                    <div class="status-info">
                        <div class="status-item">
                            <span class="label">تاريخ التفعيل:</span>
                            <span id="activationDate" class="value"></span>
                        </div>
                        <div class="status-item">
                            <span class="label">تاريخ الانتهاء:</span>
                            <span id="expiryDate" class="value"></span>
                        </div>
                        <div class="status-item">
                            <span class="label">الأيام المتبقية:</span>
                            <span id="daysRemaining" class="value"></span>
                        </div>
                        <div class="status-item">
                            <span class="label">نوع الترخيص:</span>
                            <span id="licenseType" class="value"></span>
                        </div>
                    </div>
                    <button onclick="enterApp()" class="enter-app-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        دخول التطبيق
                    </button>
                </div>

                <div class="license-error" id="licenseError" style="display: none;">
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="errorText"></span>
                    </div>
                </div>
            </div>

            <div class="license-footer">
                <p>© 2024 شركة النسور الماسية للتجارة - جميع الحقوق محفوظة</p>
                <p>تطوير: كريم واصل</p>
            </div>
        </div>
    </div>

    <!-- Floating Calculator -->
    <div id="floatingCalculator" class="floating-calculator" style="display: none;">
        <div class="calculator-header">
            <h4><i class="fas fa-calculator"></i> الآلة الحاسبة</h4>
            <button class="close-calculator" onclick="hideFloatingCalculator()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="calculator-body">
            <div class="calculator-display">
                <input type="text" id="calculatorDisplay" readonly placeholder="0">
            </div>
            <div class="calculator-buttons">
                <div class="calculator-row">
                    <button class="calc-btn calc-clear" onclick="clearCalculator()">C</button>
                    <button class="calc-btn calc-clear" onclick="clearEntry()">CE</button>
                    <button class="calc-btn calc-operator" onclick="deleteLast()">⌫</button>
                    <button class="calc-btn calc-operator" onclick="appendToDisplay('/')">/</button>
                </div>
                <div class="calculator-row">
                    <button class="calc-btn calc-number" onclick="appendToDisplay('7')">7</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('8')">8</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('9')">9</button>
                    <button class="calc-btn calc-operator" onclick="appendToDisplay('*')">×</button>
                </div>
                <div class="calculator-row">
                    <button class="calc-btn calc-number" onclick="appendToDisplay('4')">4</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('5')">5</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('6')">6</button>
                    <button class="calc-btn calc-operator" onclick="appendToDisplay('-')">-</button>
                </div>
                <div class="calculator-row">
                    <button class="calc-btn calc-number" onclick="appendToDisplay('1')">1</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('2')">2</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('3')">3</button>
                    <button class="calc-btn calc-operator" onclick="appendToDisplay('+')">+</button>
                </div>
                <div class="calculator-row">
                    <button class="calc-btn calc-number calc-zero" onclick="appendToDisplay('0')">0</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('.')">.</button>
                    <button class="calc-btn calc-equals" onclick="calculateResult()">=</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="script.js"></script>

    <!-- Auto Sync System -->
    <script src="auto-sync-system.js"></script>


</body>
</html>
