package com.waseltv.iptv.ui.diagnostics

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.waseltv.iptv.data.model.Channel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.concurrent.TimeUnit

data class DiagnosticsUiState(
    val testChannels: List<Channel> = emptyList(),
    val testResults: Map<String, StreamTestResult> = emptyMap(),
    val loadingChannels: Set<String> = emptySet(),
    val isLoading: Boolean = false
)

class DiagnosticsViewModel(application: Application) : AndroidViewModel(application) {
    
    private val _uiState = MutableStateFlow(DiagnosticsUiState())
    val uiState: StateFlow<DiagnosticsUiState> = _uiState.asStateFlow()
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(10, TimeUnit.SECONDS)
        .build()
    
    companion object {
        private const val TAG = "DiagnosticsViewModel"

        // معلومات سيرفر واصل TV
        private const val PLAYLIST_NAME = "واصل TV"
        private const val SERVER_URL = "http://maventv.one:80"
        private const val USERNAME = "odaitv"
        private const val PASSWORD = "Odai2030"
    }
    
    fun loadTestChannels() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val testChannels = createTestChannels()
            
            _uiState.value = _uiState.value.copy(
                testChannels = testChannels,
                isLoading = false
            )
        }
    }
    
    private fun createTestChannels(): List<Channel> {
        return listOf(
            // قنوات الأخبار
            Channel(
                id = "106997",
                name = "القرآن الكريم",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/106997.ts",
                logo = null,
                group = "أخبار",
                language = "العربية",
                country = "عربي",
                description = "قناة القرآن الكريم"
            ),
            Channel(
                id = "106998",
                name = "الجزيرة مباشر",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/106998.ts",
                logo = null,
                group = "أخبار",
                language = "العربية",
                country = "قطر",
                description = "قناة الجزيرة الإخبارية"
            ),
            Channel(
                id = "106999",
                name = "العربية",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/106999.ts",
                logo = null,
                group = "أخبار",
                language = "العربية",
                country = "السعودية",
                description = "قناة العربية الإخبارية"
            ),
            
            // قنوات الرياضة
            Channel(
                id = "107010",
                name = "بي إن سبورت 1 HD",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/107010.ts",
                logo = null,
                group = "رياضة",
                language = "العربية",
                country = "قطر",
                description = "قناة بي إن سبورت 1"
            ),
            Channel(
                id = "107011",
                name = "بي إن سبورت 2 HD",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/107011.ts",
                logo = null,
                group = "رياضة",
                language = "العربية",
                country = "قطر",
                description = "قناة بي إن سبورت 2"
            ),
            
            // قنوات الأطفال
            Channel(
                id = "107020",
                name = "سبيستون",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/107020.ts",
                logo = null,
                group = "أطفال",
                language = "العربية",
                country = "الإمارات",
                description = "قناة سبيستون للأطفال"
            ),
            
            // قنوات الترفيه
            Channel(
                id = "107030",
                name = "MBC 1 HD",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/107030.ts",
                logo = null,
                group = "ترفيه",
                language = "العربية",
                country = "السعودية",
                description = "قناة MBC 1"
            ),
            
            // اختبار stream IDs بسيطة
            Channel(
                id = "1",
                name = "اختبار Stream 1",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/1.ts",
                logo = null,
                group = "اختبار",
                language = "العربية",
                country = "عربي",
                description = "اختبار stream بسيط"
            ),
            Channel(
                id = "100",
                name = "اختبار Stream 100",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/100.ts",
                logo = null,
                group = "اختبار",
                language = "العربية",
                country = "عربي",
                description = "اختبار stream متوسط"
            ),
            Channel(
                id = "1000",
                name = "اختبار Stream 1000",
                url = "$SERVER_URL/live/$USERNAME/$PASSWORD/1000.ts",
                logo = null,
                group = "اختبار",
                language = "العربية",
                country = "عربي",
                description = "اختبار stream كبير"
            )
        )
    }
    
    suspend fun testStream(channel: Channel) {
        withContext(Dispatchers.IO) {
            // إضافة القناة للقائمة المحملة
            _uiState.value = _uiState.value.copy(
                loadingChannels = _uiState.value.loadingChannels + channel.id
            )
            
            val startTime = System.currentTimeMillis()
            
            try {
                Log.d(TAG, "اختبار stream: ${channel.name} - ${channel.url}")
                
                val request = Request.Builder()
                    .url(channel.url)
                    .head() // استخدام HEAD request للاختبار السريع
                    .addHeader("User-Agent", "WASEL-TV/1.0")
                    .addHeader("Accept", "*/*")
                    .build()
                
                val response = httpClient.newCall(request).execute()
                val duration = System.currentTimeMillis() - startTime
                
                val result = if (response.isSuccessful) {
                    Log.d(TAG, "✅ Stream يعمل: ${channel.name} (${response.code})")
                    StreamTestResult(
                        isSuccess = true,
                        message = "HTTP ${response.code} - ${response.message}",
                        duration = duration
                    )
                } else {
                    Log.d(TAG, "❌ Stream لا يعمل: ${channel.name} (${response.code})")
                    StreamTestResult(
                        isSuccess = false,
                        message = "HTTP ${response.code} - ${response.message}",
                        duration = duration
                    )
                }
                
                // تحديث النتائج
                _uiState.value = _uiState.value.copy(
                    testResults = _uiState.value.testResults + (channel.id to result),
                    loadingChannels = _uiState.value.loadingChannels - channel.id
                )
                
            } catch (e: Exception) {
                val duration = System.currentTimeMillis() - startTime
                Log.e(TAG, "خطأ في اختبار stream: ${channel.name}", e)
                
                val result = StreamTestResult(
                    isSuccess = false,
                    message = "خطأ: ${e.message ?: "خطأ غير معروف"}",
                    duration = duration
                )
                
                _uiState.value = _uiState.value.copy(
                    testResults = _uiState.value.testResults + (channel.id to result),
                    loadingChannels = _uiState.value.loadingChannels - channel.id
                )
            }
        }
    }
    
    suspend fun testAllStreams() {
        val channels = _uiState.value.testChannels
        Log.d(TAG, "بدء اختبار ${channels.size} stream")
        
        for (channel in channels) {
            testStream(channel)
            // تأخير بسيط بين الاختبارات لتجنب إرهاق السيرفر
            kotlinx.coroutines.delay(500)
        }
        
        Log.d(TAG, "انتهى اختبار جميع الـ streams")
    }
    
    override fun onCleared() {
        super.onCleared()
        httpClient.dispatcher.executorService.shutdown()
    }
}
