<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="100dp"
    android:height="100dp"
    android:viewportWidth="100"
    android:viewportHeight="100">

    <!-- Diamond Background -->
    <path
        android:pathData="M50,5 L85,35 L50,65 L15,35 Z"
        android:fillType="evenOdd">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient
                android:startX="0"
                android:startY="0"
                android:endX="100"
                android:endY="100"
                android:type="linear">
                <item android:offset="0" android:color="#667eea" />
                <item android:offset="0.5" android:color="#764ba2" />
                <item android:offset="1" android:color="#f093fb" />
            </gradient>
        </aapt:attr>
    </path>

    <!-- Diamond Border -->
    <path
        android:pathData="M50,5 L85,35 L50,65 L15,35 Z"
        android:strokeColor="#ffffff"
        android:strokeWidth="2"
        android:fillColor="@android:color/transparent" />

    <!-- Eagle Silhouette -->
    <path
        android:pathData="M50,20 C45,18 40,20 38,25 C36,30 40,35 45,37 L50,40 L55,37 C60,35 64,30 62,25 C60,20 55,18 50,20 Z"
        android:fillType="evenOdd">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient
                android:startX="0"
                android:startY="0"
                android:endX="100"
                android:endY="100"
                android:type="linear">
                <item android:offset="0" android:color="#ffd700" />
                <item android:offset="1" android:color="#ffb347" />
            </gradient>
        </aapt:attr>
    </path>

    <!-- Eagle Eye -->
    <circle
        android:cx="45"
        android:cy="25"
        android:r="2"
        android:fillColor="#333333" />

    <!-- Eagle Beak -->
    <path
        android:pathData="M42,28 L48,30"
        android:strokeColor="#333333"
        android:strokeWidth="1" />

    <!-- Poultry Battery Representation -->
    <rect
        android:x="25"
        android:y="70"
        android:width="50"
        android:height="25"
        android:rx="3"
        android:fillColor="#4a90e2" />

    <!-- Battery Border -->
    <rect
        android:x="25"
        android:y="70"
        android:width="50"
        android:height="25"
        android:rx="3"
        android:strokeColor="#ffffff"
        android:strokeWidth="1"
        android:fillColor="@android:color/transparent" />

    <!-- Battery Compartments -->
    <rect android:x="30" android:y="75" android:width="8" android:height="6" android:fillColor="#ffffff" android:alpha="0.8" />
    <rect android:x="42" android:y="75" android:width="8" android:height="6" android:fillColor="#ffffff" android:alpha="0.8" />
    <rect android:x="54" android:y="75" android:width="8" android:height="6" android:fillColor="#ffffff" android:alpha="0.8" />
    <rect android:x="66" android:y="75" android:width="8" android:height="6" android:fillColor="#ffffff" android:alpha="0.8" />

    <!-- Decorative Elements -->
    <circle android:cx="20" android:cy="15" android:r="2" android:fillColor="#ffd700" android:alpha="0.6" />
    <circle android:cx="80" android:cy="15" android:r="2" android:fillColor="#ffd700" android:alpha="0.6" />
    <circle android:cx="15" android:cy="50" android:r="1.5" android:fillColor="#ffd700" android:alpha="0.4" />
    <circle android:cx="85" android:cy="50" android:r="1.5" android:fillColor="#ffd700" android:alpha="0.4" />

</vector>
