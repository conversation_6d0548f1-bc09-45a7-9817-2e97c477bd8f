# تعليمات بناء تطبيق النسور الماسية

## 🎯 نظرة عامة
تم تحويل تطبيق الويب الخاص بشركة النسور الماسية إلى تطبيق Android أصلي باستخدام WebView مع تحسينات خاصة للهواتف المحمولة.

## 📁 بنية المشروع المُنشأة

```
android_app/
├── app/
│   ├── src/main/
│   │   ├── java/com/diamondeagles/inventory/
│   │   │   ├── MainActivity.java          # النشاط الرئيسي
│   │   │   └── SplashActivity.java        # شاشة البداية
│   │   ├── res/
│   │   │   ├── layout/
│   │   │   │   ├── activity_main.xml      # تخطيط النشاط الرئيسي
│   │   │   │   └── activity_splash.xml    # تخطيط شاشة البداية
│   │   │   ├── values/
│   │   │   │   ├── strings.xml            # النصوص العربية
│   │   │   │   ├── colors.xml             # ألوان التطبيق
│   │   │   │   └── themes.xml             # مظاهر التطبيق
│   │   │   ├── drawable/                  # الرسوميات والأيقونات
│   │   │   └── mipmap/                    # أيقونات التطبيق
│   │   ├── assets/
│   │   │   ├── index.html                 # واجهة التطبيق الرئيسية
│   │   │   ├── style.css                  # ملف التنسيق المحسن للموبايل
│   │   │   └── script.js                  # ملف JavaScript الأصلي
│   │   └── AndroidManifest.xml            # ملف البيان
│   └── build.gradle                       # إعدادات بناء التطبيق
├── gradle/                                # ملفات Gradle
├── build.gradle                           # إعدادات المشروع الرئيسية
├── settings.gradle                        # إعدادات المشروع
└── gradlew.bat                           # ملف بناء Windows
```

## 🛠️ طرق بناء التطبيق

### الطريقة الأولى: Android Studio (مُوصى بها)

1. **تثبيت Android Studio**
   - حمّل من [developer.android.com](https://developer.android.com/studio)
   - ثبّت Android SDK (API Level 21+)

2. **فتح المشروع**
   - افتح Android Studio
   - اختر "Open an existing project"
   - اختر مجلد `android_app`

3. **بناء APK**
   - انتظر حتى ينتهي Gradle من التحميل
   - اذهب إلى `Build` → `Build Bundle(s) / APK(s)` → `Build APK(s)`
   - انتظر حتى اكتمال البناء

4. **العثور على APK**
   - المسار: `app/build/outputs/apk/debug/app-debug.apk`

### الطريقة الثانية: سطر الأوامر

```bash
# في مجلد android_app
./gradlew assembleDebug

# أو في Windows
gradlew.bat assembleDebug
```

### الطريقة الثالثة: أدوات بديلة

إذا لم تتوفر لديك Android Studio:

1. **استخدم Android SDK Command Line Tools**
2. **أو استخدم خدمات البناء السحابية مثل:**
   - GitHub Actions
   - GitLab CI
   - Bitrise

## ✨ الميزات المُضافة للنسخة المحمولة

### 🎨 تحسينات الواجهة
- **تصميم متجاوب**: محسن للشاشات الصغيرة
- **أزرار صديقة للمس**: حد أدنى 44px للأزرار
- **منع التكبير التلقائي**: عند التركيز على الحقول
- **دعم الوضع الليلي**: يتبع إعدادات النظام

### 📱 تحسينات Android
- **شاشة بداية مخصصة**: مع شعار الشركة
- **أيقونة تطبيق احترافية**: تصميم النسر الماسي
- **دعم الاتجاهات**: عمودي وأفقي
- **تحسين الأداء**: لـ WebView

### 🔧 وظائف Android الأصلية
- **زر الخروج**: متصل بنظام Android
- **مشاركة المحتوى**: عبر نظام المشاركة
- **إشعارات Toast**: رسائل سريعة
- **معلومات الجهاز**: للدعم الفني

## 🎯 التخصيصات المُطبقة

### من تطبيق الويب الأصلي:
- ✅ نظام إدارة المنتجات كاملاً
- ✅ إدارة طلبات العملاء
- ✅ تصدير التقارير (PDF, Word, Excel)
- ✅ النسخ الاحتياطي والاستعادة
- ✅ الآلة الحاسبة المدمجة
- ✅ نظام الترخيص

### إضافات للنسخة المحمولة:
- ✅ واجهة محسنة للمس
- ✅ تنقل محسن للهواتف
- ✅ جداول قابلة للتمرير أفقياً
- ✅ أزرار أكبر وأوضح
- ✅ قوائم منسدلة محسنة

## 🔧 حل المشاكل الشائعة

### مشكلة: Gradle build failed
```bash
# تنظيف المشروع
./gradlew clean

# إعادة البناء
./gradlew assembleDebug
```

### مشكلة: SDK not found
- تأكد من تثبيت Android SDK
- اضبط متغير البيئة `ANDROID_HOME`

### مشكلة: Java version incompatible
- استخدم Java 8 أو 11
- اضبط `JAVA_HOME` بشكل صحيح

## 📊 مواصفات التطبيق النهائي

- **الحد الأدنى لـ Android**: 5.0 (API 21)
- **الهدف**: Android 13 (API 34)
- **حجم APK**: ~15-20 MB
- **الأذونات**: الإنترنت، التخزين، الشبكة
- **اللغة**: العربية (RTL)
- **الاتجاه**: عمودي (مع دعم أفقي)

## 🚀 خطوات ما بعد البناء

1. **اختبار التطبيق**
   - ثبّت على جهاز تجريبي
   - اختبر جميع الوظائف
   - تأكد من عمل التصدير

2. **توقيع التطبيق** (للإنتاج)
   ```bash
   # إنشاء keystore
   keytool -genkey -v -keystore release-key.keystore -alias alias_name -keyalg RSA -keysize 2048 -validity 10000
   
   # بناء APK موقع
   ./gradlew assembleRelease
   ```

3. **التوزيع**
   - رفع على Google Play Store
   - أو توزيع مباشر للعملاء

## 📞 الدعم الفني

إذا واجهت مشاكل في البناء:

1. تأكد من تثبيت جميع المتطلبات
2. راجع ملف `BUILD_INSTRUCTIONS.md`
3. تواصل مع فريق التطوير

---

**ملاحظة**: هذا التطبيق تم تطويره خصيصاً لشركة النسور الماسية للتجارة ومحسن لإدارة مخزون بطاريات الدواجن.
