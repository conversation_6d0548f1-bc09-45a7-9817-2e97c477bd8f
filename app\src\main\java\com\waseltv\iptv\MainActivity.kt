package com.waseltv.iptv

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BugReport
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.waseltv.iptv.ui.components.ChannelsList
import com.waseltv.iptv.ui.components.CategorizedChannelsList
import com.waseltv.iptv.ui.components.LoadingScreen
import com.waseltv.iptv.ui.components.ErrorScreen
import com.waseltv.iptv.ui.diagnostics.DiagnosticsActivity
import com.waseltv.iptv.ui.player.VideoPlayerActivity
import com.waseltv.iptv.ui.settings.SettingsActivity
import com.waseltv.iptv.ui.theme.WASELTVTheme
import com.waseltv.iptv.ui.viewmodel.MainViewModel
import com.waseltv.iptv.data.model.Channel

class MainActivity : ComponentActivity() {
    private val viewModel: MainViewModel by viewModels()

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            WASELTVTheme {
                val uiState by viewModel.uiState.collectAsStateWithLifecycle()
                val context = LocalContext.current
                
                Scaffold(
                    modifier = Modifier.fillMaxSize(),
                    topBar = {
                        TopAppBar(
                            title = { 
                                Text(
                                    text = stringResource(id = R.string.app_name),
                                    style = MaterialTheme.typography.headlineSmall
                                )
                            },
                            actions = {
                                // زر التشخيص
                                IconButton(
                                    onClick = {
                                        startActivity(Intent(context, DiagnosticsActivity::class.java))
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.BugReport,
                                        contentDescription = "تشخيص الـ Streams"
                                    )
                                }

                                // زر الإعدادات
                                IconButton(
                                    onClick = {
                                        startActivity(Intent(context, SettingsActivity::class.java))
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Settings,
                                        contentDescription = stringResource(id = R.string.settings)
                                    )
                                }
                            },
                            colors = TopAppBarDefaults.topAppBarColors(
                                containerColor = MaterialTheme.colorScheme.primary,
                                titleContentColor = MaterialTheme.colorScheme.onPrimary,
                                actionIconContentColor = MaterialTheme.colorScheme.onPrimary
                            )
                        )
                    }
                ) { paddingValues ->
                    when {
                        uiState.isLoading -> {
                            LoadingScreen(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(paddingValues)
                            )
                        }
                        
                        uiState.error != null -> {
                            ErrorScreen(
                                error = uiState.error,
                                onRetry = { viewModel.loadChannels() },
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(paddingValues)
                            )
                        }
                        
                        else -> {
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(paddingValues)
                            ) {
                                // عرض معلومات التشخيص
                                if (!uiState.debugInfo.isNullOrEmpty()) {
                                    Card(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(8.dp),
                                        colors = CardDefaults.cardColors(
                                            containerColor = if (uiState.isConnected)
                                                MaterialTheme.colorScheme.primaryContainer
                                            else MaterialTheme.colorScheme.errorContainer
                                        )
                                    ) {
                                        Column(
                                            modifier = Modifier.padding(12.dp)
                                        ) {
                                            Text(
                                                text = uiState.debugInfo,
                                                style = MaterialTheme.typography.bodySmall,
                                                color = if (uiState.isConnected)
                                                    MaterialTheme.colorScheme.onPrimaryContainer
                                                else MaterialTheme.colorScheme.onErrorContainer
                                            )

                                            // عرض معلومات المستخدم إذا كانت متاحة
                                            if (!uiState.userInfo.isNullOrEmpty()) {
                                                Text(
                                                    text = "\n📊 معلومات الحساب:\n${uiState.userInfo}",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                                )
                                            }
                                        }
                                    }
                                }

                                // عرض القنوات مقسمة حسب الفئات
                                if (uiState.channelGroups.isNotEmpty()) {
                                    CategorizedChannelsList(
                                        channelGroups = uiState.channelGroups,
                                        selectedCategory = uiState.selectedCategory,
                                        onCategorySelected = { category ->
                                            viewModel.selectCategory(category)
                                        },
                                        onChannelClick = { channel ->
                                            playChannel(channel)
                                        },
                                        modifier = Modifier.fillMaxSize()
                                    )
                                } else {
                                    // عرض القنوات العادي كـ fallback
                                    ChannelsList(
                                        channels = uiState.channels,
                                        onChannelClick = { channel ->
                                            playChannel(channel)
                                        },
                                        modifier = Modifier.fillMaxSize()
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Load channels when activity starts
        viewModel.loadChannels()
    }
    
    private fun playChannel(channel: Channel) {
        val intent = Intent(this, VideoPlayerActivity::class.java).apply {
            putExtra("channel", channel)
        }
        startActivity(intent)
    }
}
