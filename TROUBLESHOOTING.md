# دليل حل مشاكل WASEL-TV

## المشكلة الحالية: عدم عرض البثوث المباشرة

### 🔍 التشخيص الأولي

عند اختبار السيرفر `http://maventv.one:80` بالمعلومات المقدمة:
- **السيرفر:** http://maventv.one:80
- **المستخدم:** Odaitv
- **كلمة المرور:** Odai2030

**النتيجة:** السيرفر يرجع "Invalid Authorization or URL / 404 Error"

### 🛠 الحلول المطبقة في التطبيق

#### 1. اختبار endpoints متعددة
```kotlin
val endpoints = listOf(
    "$SERVER_URL/player_api.php?username=$USERNAME&password=$PASSWORD&action=get_live_streams",
    "$SERVER_URL/get.php?username=$USERNAME&password=$PASSWORD&type=m3u_plus",
    "$SERVER_URL/get.php?username=$USERNAME&password=$PASSWORD&type=m3u",
    "$SERVER_URL/xmltv.php?username=$USERNAME&password=$PASSWORD"
)
```

#### 2. تنسيقات stream مختلفة
```kotlin
val streamFormats = listOf(
    "$SERVER_URL/live/$USERNAME/$PASSWORD/{id}.ts",
    "$SERVER_URL/live/$USERNAME/$PASSWORD/{id}.m3u8",
    "$SERVER_URL/{USERNAME}/{PASSWORD}/{id}.ts"
)
```

#### 3. قنوات اختبار مدمجة
- قنوات تجريبية بأرقام مختلفة (1, 2, 3, 100, 1001, 10001)
- فيديوهات اختبار مجانية للتأكد من عمل المشغل
- تنسيقات متعددة (.ts, .m3u8)

### 📋 خطوات التشخيص

#### الخطوة 1: اختبار الاتصال الأساسي
```bash
curl -v "http://maventv.one:80"
```

#### الخطوة 2: اختبار المصادقة
```bash
curl -v "http://maventv.one:80/player_api.php?username=Odaitv&password=Odai2030"
```

#### الخطوة 3: اختبار قوائم M3U
```bash
curl -v "http://maventv.one:80/get.php?username=Odaitv&password=Odai2030&type=m3u"
```

### 🔧 الحلول المحتملة

#### 1. تحقق من صحة المعلومات
- **السيرفر:** هل الرابط صحيح؟
- **المنفذ:** هل 80 هو المنفذ الصحيح؟
- **المستخدم/كلمة المرور:** هل المعلومات صحيحة وفعالة؟

#### 2. تنسيقات بديلة للسيرفر
```
http://maventv.one/
http://maventv.one:8080/
https://maventv.one/
http://maventv.one:25461/
```

#### 3. endpoints بديلة
```
/player_api.php
/get.php
/xmltv.php
/live/
/streaming/
```

#### 4. معاملات إضافية
```
&output=ts
&output=m3u8
&action=get_live_streams
&action=get_live_categories
```

### 📱 ما يفعله التطبيق حالياً

1. **اختبار تلقائي:** يختبر عدة endpoints تلقائياً
2. **تشخيص مفصل:** يعرض معلومات التشخيص في الواجهة
3. **قنوات احتياطية:** يعرض قنوات تجريبية إذا فشل الاتصال
4. **مشغل متقدم:** يدعم تنسيقات متعددة للفيديو

### 🎯 الخطوات التالية

#### للمطور:
1. تشغيل التطبيق ومراقبة logs في Android Studio
2. فحص معلومات التشخيص في الواجهة
3. اختبار القنوات التجريبية للتأكد من عمل المشغل

#### للمستخدم:
1. التحقق من صحة معلومات السيرفر
2. تجربة معلومات سيرفر بديلة إذا كانت متاحة
3. التواصل مع مزود الخدمة للتأكد من صحة المعلومات

### 📊 معلومات إضافية

#### تنسيقات IPTV المدعومة:
- **M3U/M3U8:** قوائم تشغيل نصية
- **JSON API:** واجهة برمجية متقدمة
- **TS Streams:** بث مباشر
- **HLS:** HTTP Live Streaming

#### أخطاء شائعة:
- `Invalid Authorization`: خطأ في المستخدم/كلمة المرور
- `404 Error`: الرابط غير موجود
- `Connection Timeout`: مشكلة في الشبكة
- `Empty Response`: السيرفر لا يرجع بيانات

### 🔄 تحديثات التطبيق

#### الإصدار الحالي:
- ✅ اختبار endpoints متعددة
- ✅ تشخيص مفصل
- ✅ قنوات احتياطية
- ✅ مشغل متقدم
- ✅ واجهة عربية كاملة

#### التحسينات المضافة:
- 🔧 ServerTester للتشخيص المتقدم
- 📊 عرض معلومات التشخيص في الواجهة
- 🔄 اختبار تلقائي لـ stream URLs
- 📱 قنوات تجريبية متعددة التنسيقات
