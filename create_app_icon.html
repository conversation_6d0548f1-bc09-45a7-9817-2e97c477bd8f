<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة واصل TV</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2196F3;
            margin-bottom: 30px;
        }
        
        .icon-preview {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .icon-size {
            text-align: center;
            margin: 10px;
        }
        
        .icon {
            width: 100px;
            height: 100px;
            border-radius: 20px;
            background: linear-gradient(135deg, #2196F3, #1976D2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
            margin: 0 auto 10px auto;
            position: relative;
            overflow: hidden;
        }
        
        .icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
        }
        
        .icon-48 { width: 48px; height: 48px; font-size: 24px; border-radius: 8px; }
        .icon-72 { width: 72px; height: 72px; font-size: 36px; border-radius: 12px; }
        .icon-96 { width: 96px; height: 96px; font-size: 48px; border-radius: 16px; }
        .icon-144 { width: 144px; height: 144px; font-size: 72px; border-radius: 24px; }
        .icon-192 { width: 192px; height: 192px; font-size: 96px; border-radius: 32px; }
        
        .download-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .download-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        .download-btn:hover {
            background: #45a049;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 إنشاء أيقونة واصل TV</h1>
        
        <div class="info-box">
            <h3>📱 أيقونة التطبيق</h3>
            <p>أيقونة احترافية لتطبيق واصل TV مع تدرج لوني جميل وتأثيرات بصرية</p>
        </div>
        
        <div class="icon-preview">
            <div class="icon-size">
                <div class="icon icon-48">📺</div>
                <p>48x48</p>
            </div>
            <div class="icon-size">
                <div class="icon icon-72">📺</div>
                <p>72x72</p>
            </div>
            <div class="icon-size">
                <div class="icon icon-96">📺</div>
                <p>96x96</p>
            </div>
            <div class="icon-size">
                <div class="icon icon-144">📺</div>
                <p>144x144</p>
            </div>
            <div class="icon-size">
                <div class="icon icon-192">📺</div>
                <p>192x192</p>
            </div>
        </div>
        
        <div class="download-section">
            <h3>💾 تحميل الأيقونات</h3>
            <p>اضغط على الأزرار لتحميل الأيقونات بأحجام مختلفة:</p>
            
            <button class="download-btn" onclick="downloadIcon(48)">تحميل 48x48</button>
            <button class="download-btn" onclick="downloadIcon(72)">تحميل 72x72</button>
            <button class="download-btn" onclick="downloadIcon(96)">تحميل 96x96</button>
            <button class="download-btn" onclick="downloadIcon(144)">تحميل 144x144</button>
            <button class="download-btn" onclick="downloadIcon(192)">تحميل 192x192</button>
            
            <br><br>
            <button class="download-btn" onclick="downloadAllIcons()" style="background: #FF5722;">📦 تحميل جميع الأحجام</button>
        </div>
        
        <div class="info-box">
            <h3>📋 تعليمات الاستخدام</h3>
            <ol>
                <li>حمّل الأيقونات بالأحجام المطلوبة</li>
                <li>ضع الأيقونات في مجلدات <code>res/mipmap-*</code> في مشروع الأندرويد</li>
                <li>استخدم <code>@mipmap/ic_launcher</code> في AndroidManifest.xml</li>
                <li>اختبر التطبيق على الجهاز</li>
            </ol>
        </div>
        
        <!-- Canvas elements for generating icons -->
        <canvas id="canvas48" width="48" height="48"></canvas>
        <canvas id="canvas72" width="72" height="72"></canvas>
        <canvas id="canvas96" width="96" height="96"></canvas>
        <canvas id="canvas144" width="144" height="144"></canvas>
        <canvas id="canvas192" width="192" height="192"></canvas>
    </div>

    <script>
        function createIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#2196F3');
            gradient.addColorStop(1, '#1976D2');
            
            // Draw rounded rectangle background
            const radius = size * 0.2;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // Add shine effect
            const shineGradient = ctx.createLinearGradient(0, 0, size, size * 0.5);
            shineGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
            shineGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            ctx.fillStyle = shineGradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // Draw TV icon
            const iconSize = size * 0.6;
            const x = (size - iconSize) / 2;
            const y = (size - iconSize) / 2;
            
            // TV screen
            ctx.fillStyle = 'white';
            ctx.fillRect(x, y, iconSize, iconSize * 0.7);
            
            // TV frame
            ctx.strokeStyle = 'white';
            ctx.lineWidth = size * 0.05;
            ctx.strokeRect(x - ctx.lineWidth, y - ctx.lineWidth, 
                          iconSize + ctx.lineWidth * 2, iconSize * 0.7 + ctx.lineWidth * 2);
            
            // TV stand
            const standWidth = iconSize * 0.3;
            const standHeight = iconSize * 0.15;
            const standX = x + (iconSize - standWidth) / 2;
            const standY = y + iconSize * 0.7;
            
            ctx.fillStyle = 'white';
            ctx.fillRect(standX, standY, standWidth, standHeight);
            
            // Add "واصل" text
            ctx.fillStyle = 'rgba(33, 150, 243, 0.8)';
            ctx.font = `bold ${size * 0.15}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText('واصل', size / 2, y + iconSize * 0.35);
            
            return canvas;
        }
        
        function downloadIcon(size) {
            const canvas = createIcon(size);
            const link = document.createElement('a');
            link.download = `wasel_tv_icon_${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadAllIcons() {
            const sizes = [48, 72, 96, 144, 192];
            sizes.forEach(size => {
                setTimeout(() => downloadIcon(size), size * 10);
            });
        }
        
        // Add roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
        
        // Show welcome message
        window.onload = function() {
            setTimeout(() => {
                alert('🎨 مولد أيقونة واصل TV\n\n' +
                      'هذه الأداة تنشئ أيقونات احترافية للتطبيق بأحجام مختلفة.\n\n' +
                      '📱 الأحجام المتاحة:\n' +
                      '• 48x48 - للشاشات منخفضة الدقة\n' +
                      '• 72x72 - للشاشات متوسطة الدقة\n' +
                      '• 96x96 - للشاشات عالية الدقة\n' +
                      '• 144x144 - للشاشات فائقة الدقة\n' +
                      '• 192x192 - للشاشات عالية الدقة جداً\n\n' +
                      'حمّل الأيقونات واستخدمها في مشروع الأندرويد!');
            }, 500);
        };
    </script>
</body>
</html>
