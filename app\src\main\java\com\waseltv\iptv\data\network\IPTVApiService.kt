package com.waseltv.iptv.data.network

import android.util.Log
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.data.model.ChannelGroup
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class IPTVApiService {

    companion object {
        private const val TAG = "WASEL-TV-API"
    }

    private val xtreamApi = XtreamCodesApi()
    
    /**
     * الحصول على قائمة القنوات باستخدام Xtream Codes API
     */
    suspend fun getChannels(): List<Channel> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "بدء تحميل القنوات من Xtream Codes API...")

            // اختبار الاتصال أولاً
            val isConnected = xtreamApi.testConnection()
            Log.d(TAG, "حالة الاتصال: $isConnected")

            if (isConnected) {
                // الحصول على القنوات المباشرة
                val channels = xtreamApi.getLiveStreams()

                if (channels.isNotEmpty()) {
                    Log.d(TAG, "تم تحميل ${channels.size} قناة من Xtream Codes")
                    return@withContext channels
                } else {
                    Log.w(TAG, "لم يتم العثور على قنوات من Xtream Codes API")
                }
            } else {
                Log.w(TAG, "فشل الاتصال بـ Xtream Codes API")
            }

            // إذا فشل Xtream Codes، استخدم القنوات الافتراضية
            Log.d(TAG, "استخدام القنوات الافتراضية...")
            return@withContext getDefaultChannels()

        } catch (e: Exception) {
            Log.e(TAG, "خطأ في تحميل القنوات من Xtream Codes", e)
            return@withContext getDefaultChannels()
        }
    }
    
    /**
     * اختبار الاتصال بـ Xtream Codes API
     */
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        return@withContext xtreamApi.testConnection()
    }

    /**
     * الحصول على معلومات المستخدم
     */
    suspend fun getUserInfo(): String = withContext(Dispatchers.IO) {
        return@withContext xtreamApi.getUserInfo()
    }

    /**
     * الحصول على فئات القنوات
     */
    suspend fun getCategories(): List<String> = withContext(Dispatchers.IO) {
        return@withContext xtreamApi.getLiveCategories()
    }

    /**
     * الحصول على القنوات مقسمة حسب الفئات
     */
    suspend fun getChannelGroups(): List<ChannelGroup> = withContext(Dispatchers.IO) {
        return@withContext xtreamApi.getChannelGroups()
    }

    
    /**
     * قنوات افتراضية بتنسيق Xtream Codes
     */
    private fun getDefaultChannels(): List<Channel> {
        Log.d(TAG, "إنشاء قنوات افتراضية بتنسيق Xtream Codes...")

        val channels = mutableListOf<Channel>()

        // قنوات تجريبية بأرقام مختلفة (Xtream Codes format)
        val testStreamIds = listOf(
            "1", "2", "3", "4", "5", "10", "11", "12", "20", "21",
            "100", "101", "102", "200", "201", "1000", "1001", "1002"
        )

        for ((index, streamId) in testStreamIds.withIndex()) {
            // تنسيق Xtream Codes الصحيح
            val streamUrl = "http://maventv.one:80/live/odaitv/Odai2030/$streamId.ts"

            channels.add(Channel(
                id = streamId,
                name = "قناة تجريبية $streamId",
                url = streamUrl,
                logo = null,
                group = when (index % 5) {
                    0 -> "أخبار"
                    1 -> "رياضة"
                    2 -> "ترفيه"
                    3 -> "أطفال"
                    else -> "عام"
                },
                language = "العربية",
                country = "عربي",
                description = "قناة تجريبية Xtream Codes - Stream ID: $streamId"
            ))
        }

        // إضافة قنوات اختبار مجانية للتأكد من عمل المشغل
        channels.addAll(listOf(
            Channel(
                id = "test_1",
                name = "اختبار الفيديو 1",
                url = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                logo = null,
                group = "اختبار",
                language = "English",
                country = "Test",
                description = "فيديو اختبار للتأكد من عمل المشغل"
            ),
            Channel(
                id = "test_2",
                name = "اختبار الفيديو 2",
                url = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                logo = null,
                group = "اختبار",
                language = "English",
                country = "Test",
                description = "فيديو اختبار آخر"
            )
        ))

        Log.d(TAG, "تم إنشاء ${channels.size} قناة افتراضية")
        return channels
    }

}
