@echo off
echo ========================================
echo    بناء تطبيق النسور الماسية للاندرويد
echo    Diamond Eagles Inventory APK Builder
echo ========================================
echo.

echo جاري التحقق من متطلبات البناء...
echo Checking build requirements...

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Java غير مثبت على النظام
    echo Error: Java is not installed
    pause
    exit /b 1
)

REM Check if Android SDK is available
if not exist "%ANDROID_HOME%" (
    echo تحذير: ANDROID_HOME غير محدد
    echo Warning: ANDROID_HOME not set
    echo سيتم استخدام Android SDK المدمج مع Android Studio
    echo Using Android Studio's bundled SDK
)

echo.
echo جاري تنظيف المشروع...
echo Cleaning project...
call gradlew.bat clean

if %errorlevel% neq 0 (
    echo خطأ في تنظيف المشروع
    echo Error cleaning project
    pause
    exit /b 1
)

echo.
echo جاري بناء APK...
echo Building APK...
call gradlew.bat assembleDebug

if %errorlevel% neq 0 (
    echo خطأ في بناء APK
    echo Error building APK
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم بناء APK بنجاح!
echo APK built successfully!
echo ========================================
echo.

echo مكان الملف:
echo File location:
echo %cd%\app\build\outputs\apk\debug\app-debug.apk
echo.

REM Copy APK to root directory for easy access
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    copy "app\build\outputs\apk\debug\app-debug.apk" "النسور_الماسية_v1.0.apk"
    echo تم نسخ APK إلى المجلد الرئيسي باسم: النسور_الماسية_v1.0.apk
    echo APK copied to root directory as: النسور_الماسية_v1.0.apk
)

echo.
echo يمكنك الآن تثبيت التطبيق على هاتفك الأندرويد
echo You can now install the app on your Android device
echo.

pause
