@echo off
echo ========================================
echo        بناء تطبيق واصل TV
echo ========================================
echo.

echo 📂 المسار الحالي: %CD%
echo.

echo 🔧 تنظيف المشروع...
call gradlew clean

echo.
echo 📱 بناء APK للتطبيق...
call gradlew assembleDebug

echo.
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ تم بناء APK بنجاح!
    echo.
    echo 📍 المسار الكامل لملف APK:
    echo %CD%\app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 📁 افتح المجلد:
    explorer "%CD%\app\build\outputs\apk\debug"
    echo.
    echo 📋 معلومات الملف:
    dir "app\build\outputs\apk\debug\app-debug.apk"
) else (
    echo ❌ فشل في بناء APK!
    echo تحقق من الأخطاء أعلاه
)

echo.
echo 📋 معلومات التطبيق:
echo اسم التطبيق: واصل TV
echo الإصدار: 1.0.0
echo Package: com.waseltv.iptv.debug
echo.

echo 🚀 لتثبيت التطبيق على الجهاز:
echo adb install "%CD%\app\build\outputs\apk\debug\app-debug.apk"
echo.

echo 📱 أو انسخ ملف APK إلى الجهاز وثبته يدوياً
echo.

pause
