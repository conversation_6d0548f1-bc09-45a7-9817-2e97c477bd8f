<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - شركة النسور الماسية للتجارة</title>
    
    <!-- Fonts & Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .login-header .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .login-header h1 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .login-header p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 1.1rem;
        }

        .form-group input {
            padding-left: 50px;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #c33;
            font-size: 0.9rem;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #363;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #363;
            font-size: 0.9rem;
            display: none;
        }

        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }

        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .browse-mode-section {
            margin-top: 30px;
            text-align: center;
        }

        .divider {
            position: relative;
            margin: 20px 0;
            text-align: center;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
        }

        .divider span {
            background: white;
            padding: 0 15px;
            color: #999;
            font-size: 0.9rem;
        }

        .browse-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .browse-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }

        .browse-btn:active {
            transform: translateY(0);
        }

        .browse-note {
            font-size: 0.85rem;
            color: #666;
            margin: 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }

        .browse-note i {
            color: #28a745;
            margin-left: 5px;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading i {
            animation: spin 1s linear infinite;
            color: #667eea;
            font-size: 1.5rem;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .login-container {
                margin: 0;
                border-radius: 15px;
                max-width: none;
                width: 100%;
            }

            .login-header {
                padding: 25px 20px;
            }

            .login-form {
                padding: 25px 20px;
            }

            .login-header h1 {
                font-size: 1.4rem;
                line-height: 1.3;
            }

            .login-header p {
                font-size: 0.85rem;
            }

            .form-group input {
                padding: 16px 50px 16px 20px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .login-btn, .browse-btn {
                padding: 16px;
                font-size: 1rem;
            }

            .browse-note {
                font-size: 0.8rem;
                padding: 8px;
            }
        }

        @media (max-width: 480px) {
            .login-header .logo {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .login-header h1 {
                font-size: 1.2rem;
            }

            .login-header p {
                font-size: 0.8rem;
            }

            .form-group input {
                padding: 14px 45px 14px 18px;
            }

            .form-group i {
                left: 12px;
                font-size: 1rem;
            }
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .login-btn, .browse-btn {
                min-height: 48px;
            }

            .form-group input {
                min-height: 48px;
            }

            .forgot-password a {
                padding: 10px;
                display: inline-block;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-gem"></i>
            </div>
            <h1>شركة النسور الماسية للتجارة</h1>
            <p>نظام إدارة مخزون بطاريات الدواجن</p>
        </div>
        
        <div class="login-form">
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="errorText"></span>
            </div>
            
            <div class="success-message" id="successMessage">
                <i class="fas fa-check-circle"></i>
                <span id="successText"></span>
            </div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <div style="position: relative;">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" placeholder="أدخل البريد الإلكتروني" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div style="position: relative;">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" placeholder="أدخل كلمة المرور" required>
                    </div>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>

            <div class="browse-mode-section">
                <div class="divider">
                    <span>أو</span>
                </div>

                <button type="button" class="browse-btn" onclick="enterBrowseMode()">
                    <i class="fas fa-eye"></i>
                    الدخول في وضع التصفح
                </button>

                <p class="browse-note">
                    <i class="fas fa-info-circle"></i>
                    وضع التصفح يتيح لك عرض المنتجات فقط بدون إمكانية التعديل
                </p>
            </div>

            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>جاري التحقق من البيانات...</p>
            </div>

            <div class="forgot-password">
                <a href="#" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
            </div>
        </div>
    </div>

    <script>
        // Default login credentials
        const DEFAULT_CREDENTIALS = {
            email: '<EMAIL>',
            password: '2030'
        };

        // Load saved credentials from localStorage
        function loadSavedCredentials() {
            const saved = localStorage.getItem('loginCredentials');
            if (saved) {
                try {
                    return JSON.parse(saved);
                } catch (error) {
                    console.error('خطأ في قراءة بيانات الدخول المحفوظة:', error);
                }
            }
            return DEFAULT_CREDENTIALS;
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            const successDiv = document.getElementById('successMessage');
            
            successDiv.style.display = 'none';
            errorText.textContent = message;
            errorDiv.style.display = 'block';
            
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            const successText = document.getElementById('successText');
            const errorDiv = document.getElementById('errorMessage');
            
            errorDiv.style.display = 'none';
            successText.textContent = message;
            successDiv.style.display = 'block';
        }

        // Show loading state
        function showLoading(show) {
            const loading = document.getElementById('loading');
            const loginBtn = document.getElementById('loginBtn');
            const form = document.getElementById('loginForm');
            
            if (show) {
                loading.style.display = 'block';
                loginBtn.style.display = 'none';
                form.style.opacity = '0.7';
            } else {
                loading.style.display = 'none';
                loginBtn.style.display = 'block';
                form.style.opacity = '1';
            }
        }

        // Handle login form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showError('يرجى إدخال البريد الإلكتروني وكلمة المرور');
                return;
            }
            
            showLoading(true);
            
            // Simulate loading delay
            setTimeout(() => {
                const credentials = loadSavedCredentials();
                
                if (email === credentials.email && password === credentials.password) {
                    showSuccess('تم تسجيل الدخول بنجاح');
                    
                    // Save login session
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('currentUser', email);
                    localStorage.setItem('loginTime', new Date().toISOString());
                    
                    // Redirect to main app after 1 second
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                } else {
                    showLoading(false);
                    showError('البريد الإلكتروني أو كلمة المرور غير صحيحة');
                }
            }, 1500);
        });

        // Enter browse mode
        function enterBrowseMode() {
            showLoading(true);

            setTimeout(() => {
                showSuccess('تم الدخول في وضع التصفح');

                // Save browse mode session
                localStorage.setItem('isBrowseMode', 'true');
                localStorage.setItem('isLoggedIn', 'true'); // Allow access to app
                localStorage.setItem('currentUser', 'زائر - وضع التصفح');
                localStorage.setItem('loginTime', new Date().toISOString());

                // Redirect to main app after 1 second
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            }, 1000);
        }

        // Show forgot password (placeholder)
        function showForgotPassword() {
            alert('للحصول على كلمة مرور جديدة، يرجى التواصل مع المطور');
        }

        // Check if already logged in
        window.addEventListener('load', function() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn === 'true') {
                // Check if login is still valid (24 hours)
                const loginTime = localStorage.getItem('loginTime');
                if (loginTime) {
                    const loginDate = new Date(loginTime);
                    const now = new Date();
                    const hoursDiff = (now - loginDate) / (1000 * 60 * 60);
                    
                    if (hoursDiff < 24) {
                        // Still logged in, redirect to main app
                        window.location.href = 'index.html';
                        return;
                    }
                }
                
                // Login expired, clear session
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('loginTime');
            }
        });
    </script>
</body>
</html>
