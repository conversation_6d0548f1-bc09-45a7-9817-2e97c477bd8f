package com.waseltv.iptv.data.network

import com.waseltv.iptv.data.model.Channel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.UUID

class M3UParser {
    private val httpClient = OkHttpClient.Builder()
        .followRedirects(true)
        .followSslRedirects(true)
        .build()
    
    suspend fun parseFromUrl(url: String): List<Channel> = withContext(Dispatchers.IO) {
        val request = Request.Builder()
            .url(url)
            .build()
        
        val response = httpClient.newCall(request).execute()
        if (!response.isSuccessful) {
            throw Exception("Failed to download playlist: ${response.code}")
        }
        
        val content = response.body?.string() ?: throw Exception("Empty playlist content")
        return@withContext parseM3UContent(content)
    }
    
    fun parseM3UContent(content: String): List<Channel> {
        val channels = mutableListOf<Channel>()
        val lines = content.lines()
        
        var i = 0
        while (i < lines.size) {
            val line = lines[i].trim()
            
            if (line.startsWith("#EXTINF:")) {
                // Parse channel info from EXTINF line
                val channelInfo = parseExtInfLine(line)
                
                // Get the next non-empty line as the stream URL
                var streamUrl = ""
                for (j in (i + 1) until lines.size) {
                    val nextLine = lines[j].trim()
                    if (nextLine.isNotEmpty() && !nextLine.startsWith("#")) {
                        streamUrl = nextLine
                        i = j
                        break
                    }
                }
                
                if (streamUrl.isNotEmpty()) {
                    val channel = Channel(
                        id = UUID.randomUUID().toString(),
                        name = channelInfo["name"] ?: "Unknown Channel",
                        url = streamUrl,
                        logo = channelInfo["logo"],
                        group = channelInfo["group"],
                        language = channelInfo["language"],
                        country = channelInfo["country"],
                        description = channelInfo["description"]
                    )
                    channels.add(channel)
                }
            }
            i++
        }
        
        return channels
    }
    
    private fun parseExtInfLine(line: String): Map<String, String> {
        val info = mutableMapOf<String, String>()
        
        // Extract channel name (usually at the end after comma)
        val commaIndex = line.lastIndexOf(',')
        if (commaIndex != -1 && commaIndex < line.length - 1) {
            info["name"] = line.substring(commaIndex + 1).trim()
        }
        
        // Extract attributes using regex
        val attributePattern = """(\w+)="([^"]*)"|\s(\w+)=([^\s,]+)""".toRegex()
        attributePattern.findAll(line).forEach { match ->
            val key = (match.groups[1]?.value ?: match.groups[3]?.value)?.lowercase()
            val value = match.groups[2]?.value ?: match.groups[4]?.value
            
            if (key != null && value != null) {
                when (key) {
                    "tvg-logo", "logo" -> info["logo"] = value
                    "group-title", "group" -> info["group"] = value
                    "tvg-language", "language" -> info["language"] = value
                    "tvg-country", "country" -> info["country"] = value
                    "tvg-name", "title" -> {
                        if (!info.containsKey("name")) {
                            info["name"] = value
                        }
                    }
                }
            }
        }
        
        return info
    }
}
