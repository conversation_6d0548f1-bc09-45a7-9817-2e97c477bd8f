// Permissions Control System for Diamond Eagles Inventory
// نظام التحكم في الصلاحيات لتطبيق النسور الماسية

console.log('🔐 تحميل نظام التحكم في الصلاحيات...');

/**
 * Apply permissions-based restrictions to UI
 */
function applyPermissionsRestrictions() {
    const currentUser = userManager.getCurrentUser();

    if (!currentUser) {
        console.log('⚠️ لا يوجد مستخدم مسجل دخول - محاولة تسجيل دخول تلقائي');
        // Try to auto-login with default admin credentials
        autoLoginDefaultAdmin();
        return;
    }
    
    console.log('🔒 تطبيق قيود الصلاحيات للمستخدم:', currentUser.name);
    
    // Hide/show navigation items based on permissions
    applyNavigationRestrictions(currentUser);
    
    // Hide/show dashboard sections based on permissions
    applyDashboardRestrictions(currentUser);
    
    // Hide/show action buttons based on permissions
    applyActionButtonsRestrictions(currentUser);
    
    // Hide/show settings sections based on permissions
    applySettingsRestrictions(currentUser);
}

/**
 * Apply navigation restrictions
 */
function applyNavigationRestrictions(user) {
    const navigationItems = {
        'nav-dashboard': PERMISSIONS.VIEW_DASHBOARD,
        'nav-products': PERMISSIONS.VIEW_PRODUCTS,
        'nav-customers': PERMISSIONS.VIEW_CUSTOMERS,
        'nav-settings': PERMISSIONS.VIEW_SETTINGS
    };
    
    Object.entries(navigationItems).forEach(([navClass, permission]) => {
        const navItem = document.querySelector(`.${navClass}`);
        if (navItem) {
            const parentLi = navItem.closest('li');
            if (parentLi) {
                if (user.permissions.includes(permission)) {
                    parentLi.style.display = 'block';
                } else {
                    parentLi.style.display = 'none';
                }
            }
        }
    });
}

/**
 * Apply dashboard restrictions
 */
function applyDashboardRestrictions(user) {
    // Hide dashboard sections based on permissions
    const dashboardSections = document.querySelectorAll('#dashboard .dashboard-card');
    
    dashboardSections.forEach(section => {
        const sectionTitle = section.querySelector('h3')?.textContent || '';
        
        // Products section
        if (sectionTitle.includes('المنتجات') || section.querySelector('.btn[onclick*="products"]')) {
            if (!user.permissions.includes(PERMISSIONS.VIEW_PRODUCTS)) {
                section.style.display = 'none';
            }
        }
        
        // Customers section
        if (sectionTitle.includes('العملاء') || section.querySelector('.btn[onclick*="customers"]')) {
            if (!user.permissions.includes(PERMISSIONS.VIEW_CUSTOMERS)) {
                section.style.display = 'none';
            }
        }
        
        // Settings section
        if (sectionTitle.includes('الإعدادات') || section.querySelector('.btn[onclick*="settings"]')) {
            if (!user.permissions.includes(PERMISSIONS.VIEW_SETTINGS)) {
                section.style.display = 'none';
            }
        }
    });
}

/**
 * Apply action buttons restrictions
 */
function applyActionButtonsRestrictions(user) {
    // Products action buttons
    if (!user.permissions.includes(PERMISSIONS.ADD_PRODUCTS)) {
        hideElements('.btn[onclick*="addProduct"], .btn[onclick*="showAddProductModal"]');
    }
    
    if (!user.permissions.includes(PERMISSIONS.EDIT_PRODUCTS)) {
        hideElements('.btn[onclick*="editProduct"], .btn[onclick*="showEditProductModal"]');
    }
    
    if (!user.permissions.includes(PERMISSIONS.DELETE_PRODUCTS)) {
        hideElements('.btn[onclick*="deleteProduct"]');
    }
    
    // Customers action buttons
    if (!user.permissions.includes(PERMISSIONS.ADD_CUSTOMERS)) {
        hideElements('.btn[onclick*="addCustomer"], .btn[onclick*="showAddCustomerModal"]');
    }
    
    if (!user.permissions.includes(PERMISSIONS.EDIT_CUSTOMERS)) {
        hideElements('.btn[onclick*="editCustomer"], .btn[onclick*="showEditCustomerModal"]');
    }
    
    if (!user.permissions.includes(PERMISSIONS.DELETE_CUSTOMERS)) {
        hideElements('.btn[onclick*="deleteCustomer"]');
    }
    
    // Settings buttons
    if (!user.permissions.includes(PERMISSIONS.EDIT_SETTINGS)) {
        hideElements('#settings .btn[type="submit"], #settings .btn[onclick*="save"]');
    }
}

/**
 * Apply settings restrictions
 */
function applySettingsRestrictions(user) {
    if (!user.permissions.includes(PERMISSIONS.VIEW_SETTINGS)) {
        const settingsSection = document.getElementById('settings');
        if (settingsSection) {
            settingsSection.style.display = 'none';
        }
        return;
    }
    
    // Hide user management section if no permission
    if (!user.permissions.includes(PERMISSIONS.MANAGE_USERS)) {
        const userManagementCard = document.querySelector('.settings-card h3[contains("إدارة المستخدمين")]')?.closest('.settings-card');
        if (userManagementCard) {
            userManagementCard.style.display = 'none';
        }
    }
    
    // Hide sensitive settings for non-admin users
    if (user.role !== USER_ROLES.ADMIN) {
        hideElements('#settings .developer-actions, #settings .system-actions');
    }
}

/**
 * Hide elements by selector
 */
function hideElements(selector) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        element.style.display = 'none';
    });
}

/**
 * Show elements by selector
 */
function showElements(selector) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        element.style.display = '';
    });
}

/**
 * Check if current user has permission
 */
function hasPermission(permission) {
    const currentUser = userManager.getCurrentUser();
    if (!currentUser) return false;
    return currentUser.permissions.includes(permission);
}

/**
 * Redirect to appropriate section based on permissions
 */
function redirectToAllowedSection() {
    const currentUser = userManager.getCurrentUser();
    if (!currentUser) return;
    
    // Find first allowed section
    if (currentUser.permissions.includes(PERMISSIONS.VIEW_DASHBOARD)) {
        showSection('dashboard');
    } else if (currentUser.permissions.includes(PERMISSIONS.VIEW_PRODUCTS)) {
        showSection('products');
    } else if (currentUser.permissions.includes(PERMISSIONS.VIEW_CUSTOMERS)) {
        showSection('customers');
    } else if (currentUser.permissions.includes(PERMISSIONS.VIEW_SETTINGS)) {
        showSection('settings');
    } else {
        // No permissions - show error or logout
        if (typeof showToast === 'function') {
            showToast('ليس لديك صلاحيات للوصول لأي قسم', 'error');
        }
        setTimeout(() => {
            logoutUser();
        }, 2000);
    }
}

/**
 * Override showSection function to check permissions
 */
const originalShowSection = window.showSection;
window.showSection = function(sectionName) {
    const currentUser = userManager.getCurrentUser();
    if (!currentUser) {
        console.log('⚠️ لا يوجد مستخدم مسجل دخول');
        return;
    }
    
    // Check permissions for each section
    const sectionPermissions = {
        'dashboard': PERMISSIONS.VIEW_DASHBOARD,
        'products': PERMISSIONS.VIEW_PRODUCTS,
        'customers': PERMISSIONS.VIEW_CUSTOMERS,
        'settings': PERMISSIONS.VIEW_SETTINGS
    };
    
    const requiredPermission = sectionPermissions[sectionName];
    if (requiredPermission && !currentUser.permissions.includes(requiredPermission)) {
        if (typeof showToast === 'function') {
            showToast('ليس لديك صلاحية للوصول لهذا القسم', 'error');
        }
        return;
    }
    
    // Call original function if permission is granted
    if (originalShowSection) {
        originalShowSection(sectionName);
    }
};

/**
 * Auto-login with default admin credentials
 */
function autoLoginDefaultAdmin() {
    try {
        console.log('🔐 محاولة تسجيل دخول تلقائي للمدير الافتراضي...');

        // Default admin credentials
        const defaultEmail = '<EMAIL>';
        const defaultPassword = '2030';

        // Check if default admin exists
        const allUsers = userManager.getAllUsers();
        const defaultAdmin = allUsers.find(user => user.email === defaultEmail);

        if (!defaultAdmin) {
            console.log('👤 المدير الافتراضي غير موجود - إنشاء حساب جديد...');
            // Create default admin user
            const adminData = {
                name: 'المدير الرئيسي',
                email: defaultEmail,
                password: defaultPassword,
                role: USER_ROLES.ADMIN,
                permissions: ROLE_PERMISSIONS[USER_ROLES.ADMIN]
            };

            const createResult = userManager.addUser(adminData);
            if (!createResult.success) {
                console.error('❌ فشل في إنشاء المدير الافتراضي:', createResult.error);
                showLoginPrompt();
                return;
            }
            console.log('✅ تم إنشاء المدير الافتراضي بنجاح');
        }

        // Try to authenticate
        const result = userManager.authenticateUser(defaultEmail, defaultPassword);

        if (result.success) {
            console.log('✅ تم تسجيل الدخول التلقائي بنجاح');
            // Apply permissions after successful login
            setTimeout(() => {
                updateUserDisplay();
                applyPermissionsRestrictions();
                redirectToAllowedSection();
                showWelcomeMessage();
            }, 500);
        } else {
            console.log('❌ فشل في تسجيل الدخول التلقائي:', result.error);
            // Show login prompt or redirect to login
            showLoginPrompt();
        }
    } catch (error) {
        console.error('❌ خطأ في تسجيل الدخول التلقائي:', error);
        showLoginPrompt();
    }
}

/**
 * Show login prompt for user
 */
function showLoginPrompt() {
    // Create a simple login prompt
    const loginPrompt = document.createElement('div');
    loginPrompt.id = 'loginPrompt';
    loginPrompt.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
    `;

    loginPrompt.innerHTML = `
        <div style="background: white; padding: 30px; border-radius: 10px; text-align: center; max-width: 400px;">
            <h2 style="color: #333; margin-bottom: 20px;">🔐 تسجيل الدخول مطلوب</h2>
            <p style="color: #666; margin-bottom: 20px;">يجب تسجيل الدخول للوصول للنظام</p>

            <div style="margin-bottom: 15px;">
                <input type="email" id="loginEmail" placeholder="البريد الإلكتروني"
                       value="<EMAIL>"
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box;">
            </div>

            <div style="margin-bottom: 20px;">
                <input type="password" id="loginPassword" placeholder="كلمة المرور"
                       value="2030"
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box;">
            </div>

            <button onclick="performLogin()"
                    style="background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; width: 100%;">
                تسجيل الدخول
            </button>

            <p style="font-size: 12px; color: #999; margin-top: 15px;">
                المدير الافتراضي: <EMAIL> / 2030
            </p>
        </div>
    `;

    document.body.appendChild(loginPrompt);
}

/**
 * Perform login from prompt
 */
function performLogin() {
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;

    if (!email || !password) {
        alert('يرجى إدخال البريد الإلكتروني وكلمة المرور');
        return;
    }

    const result = userManager.authenticateUser(email, password);

    if (result.success) {
        // Remove login prompt
        const loginPrompt = document.getElementById('loginPrompt');
        if (loginPrompt) {
            loginPrompt.remove();
        }

        console.log('✅ تم تسجيل الدخول بنجاح');

        // Apply permissions and update UI
        setTimeout(() => {
            updateUserDisplay();
            applyPermissionsRestrictions();
            redirectToAllowedSection();
            showWelcomeMessage();
        }, 500);

    } else {
        alert('خطأ في تسجيل الدخول: ' + result.error);
    }
}

/**
 * Initialize permissions control
 */
function initializePermissionsControl() {
    // Apply restrictions after a delay to ensure DOM is ready
    setTimeout(() => {
        applyPermissionsRestrictions();
        redirectToAllowedSection();
    }, 1500);
}

/**
 * Update permissions when user changes
 */
function updatePermissionsForUser() {
    applyPermissionsRestrictions();
}

/**
 * Check and ensure user is logged in
 */
function ensureUserLoggedIn() {
    const currentUser = userManager.getCurrentUser();

    if (!currentUser) {
        console.log('🔐 لا يوجد مستخدم مسجل دخول - بدء عملية التسجيل');
        autoLoginDefaultAdmin();
    } else {
        console.log('✅ المستخدم مسجل دخول:', currentUser.name);
        applyPermissionsRestrictions();
        redirectToAllowedSection();
    }
}

/**
 * Initialize user session and permissions
 */
function initializeUserSession() {
    // Wait for user management system to be ready
    if (typeof userManager === 'undefined') {
        console.log('⏳ انتظار تحميل نظام إدارة المستخدمين...');
        setTimeout(initializeUserSession, 1000);
        return;
    }

    console.log('🔐 تهيئة جلسة المستخدم والصلاحيات...');
    ensureUserLoggedIn();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeUserSession();
    }, 2000);
});

/**
 * Update user display in UI
 */
function updateUserDisplay() {
    const currentUser = userManager.getCurrentUser();

    // Update current user display in settings
    const currentUserDisplay = document.getElementById('currentUserDisplay');
    if (currentUserDisplay && currentUser) {
        currentUserDisplay.textContent = currentUser.email;
    }

    // Update last login display
    const lastLoginDisplay = document.getElementById('lastLoginDisplay');
    if (lastLoginDisplay && currentUser && currentUser.lastLogin) {
        const lastLogin = new Date(currentUser.lastLogin).toLocaleString('ar-SA');
        lastLoginDisplay.textContent = lastLogin;
    }

    // Update any other user-related UI elements
    const userNameElements = document.querySelectorAll('.current-user-name');
    userNameElements.forEach(element => {
        if (currentUser) {
            element.textContent = currentUser.name;
        }
    });
}

/**
 * Show welcome message for logged in user
 */
function showWelcomeMessage() {
    const currentUser = userManager.getCurrentUser();
    if (currentUser && typeof showToast === 'function') {
        showToast(`مرحباً ${currentUser.name}`, 'success');
    }
}

// Export functions for global use
window.applyPermissionsRestrictions = applyPermissionsRestrictions;
window.hasPermission = hasPermission;
window.updatePermissionsForUser = updatePermissionsForUser;
window.ensureUserLoggedIn = ensureUserLoggedIn;
window.autoLoginDefaultAdmin = autoLoginDefaultAdmin;
window.performLogin = performLogin;

console.log('✅ تم تحميل نظام التحكم في الصلاحيات بنجاح');
