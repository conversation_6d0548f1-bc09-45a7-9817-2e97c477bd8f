// Permissions Control System for Diamond Eagles Inventory
// نظام التحكم في الصلاحيات لتطبيق النسور الماسية

console.log('🔐 تحميل نظام التحكم في الصلاحيات...');

/**
 * Apply permissions-based restrictions to UI
 */
function applyPermissionsRestrictions() {
    const currentUser = userManager.getCurrentUser();
    
    if (!currentUser) {
        console.log('⚠️ لا يوجد مستخدم مسجل دخول');
        return;
    }
    
    console.log('🔒 تطبيق قيود الصلاحيات للمستخدم:', currentUser.name);
    
    // Hide/show navigation items based on permissions
    applyNavigationRestrictions(currentUser);
    
    // Hide/show dashboard sections based on permissions
    applyDashboardRestrictions(currentUser);
    
    // Hide/show action buttons based on permissions
    applyActionButtonsRestrictions(currentUser);
    
    // Hide/show settings sections based on permissions
    applySettingsRestrictions(currentUser);
}

/**
 * Apply navigation restrictions
 */
function applyNavigationRestrictions(user) {
    const navigationItems = {
        'nav-dashboard': PERMISSIONS.VIEW_DASHBOARD,
        'nav-products': PERMISSIONS.VIEW_PRODUCTS,
        'nav-customers': PERMISSIONS.VIEW_CUSTOMERS,
        'nav-settings': PERMISSIONS.VIEW_SETTINGS
    };
    
    Object.entries(navigationItems).forEach(([navClass, permission]) => {
        const navItem = document.querySelector(`.${navClass}`);
        if (navItem) {
            const parentLi = navItem.closest('li');
            if (parentLi) {
                if (user.permissions.includes(permission)) {
                    parentLi.style.display = 'block';
                } else {
                    parentLi.style.display = 'none';
                }
            }
        }
    });
}

/**
 * Apply dashboard restrictions
 */
function applyDashboardRestrictions(user) {
    // Hide dashboard sections based on permissions
    const dashboardSections = document.querySelectorAll('#dashboard .dashboard-card');
    
    dashboardSections.forEach(section => {
        const sectionTitle = section.querySelector('h3')?.textContent || '';
        
        // Products section
        if (sectionTitle.includes('المنتجات') || section.querySelector('.btn[onclick*="products"]')) {
            if (!user.permissions.includes(PERMISSIONS.VIEW_PRODUCTS)) {
                section.style.display = 'none';
            }
        }
        
        // Customers section
        if (sectionTitle.includes('العملاء') || section.querySelector('.btn[onclick*="customers"]')) {
            if (!user.permissions.includes(PERMISSIONS.VIEW_CUSTOMERS)) {
                section.style.display = 'none';
            }
        }
        
        // Settings section
        if (sectionTitle.includes('الإعدادات') || section.querySelector('.btn[onclick*="settings"]')) {
            if (!user.permissions.includes(PERMISSIONS.VIEW_SETTINGS)) {
                section.style.display = 'none';
            }
        }
    });
}

/**
 * Apply action buttons restrictions
 */
function applyActionButtonsRestrictions(user) {
    // Products action buttons
    if (!user.permissions.includes(PERMISSIONS.ADD_PRODUCTS)) {
        hideElements('.btn[onclick*="addProduct"], .btn[onclick*="showAddProductModal"]');
    }
    
    if (!user.permissions.includes(PERMISSIONS.EDIT_PRODUCTS)) {
        hideElements('.btn[onclick*="editProduct"], .btn[onclick*="showEditProductModal"]');
    }
    
    if (!user.permissions.includes(PERMISSIONS.DELETE_PRODUCTS)) {
        hideElements('.btn[onclick*="deleteProduct"]');
    }
    
    // Customers action buttons
    if (!user.permissions.includes(PERMISSIONS.ADD_CUSTOMERS)) {
        hideElements('.btn[onclick*="addCustomer"], .btn[onclick*="showAddCustomerModal"]');
    }
    
    if (!user.permissions.includes(PERMISSIONS.EDIT_CUSTOMERS)) {
        hideElements('.btn[onclick*="editCustomer"], .btn[onclick*="showEditCustomerModal"]');
    }
    
    if (!user.permissions.includes(PERMISSIONS.DELETE_CUSTOMERS)) {
        hideElements('.btn[onclick*="deleteCustomer"]');
    }
    
    // Settings buttons
    if (!user.permissions.includes(PERMISSIONS.EDIT_SETTINGS)) {
        hideElements('#settings .btn[type="submit"], #settings .btn[onclick*="save"]');
    }
}

/**
 * Apply settings restrictions
 */
function applySettingsRestrictions(user) {
    if (!user.permissions.includes(PERMISSIONS.VIEW_SETTINGS)) {
        const settingsSection = document.getElementById('settings');
        if (settingsSection) {
            settingsSection.style.display = 'none';
        }
        return;
    }
    
    // Hide user management section if no permission
    if (!user.permissions.includes(PERMISSIONS.MANAGE_USERS)) {
        const userManagementCard = document.querySelector('.settings-card h3[contains("إدارة المستخدمين")]')?.closest('.settings-card');
        if (userManagementCard) {
            userManagementCard.style.display = 'none';
        }
    }
    
    // Hide sensitive settings for non-admin users
    if (user.role !== USER_ROLES.ADMIN) {
        hideElements('#settings .developer-actions, #settings .system-actions');
    }
}

/**
 * Hide elements by selector
 */
function hideElements(selector) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        element.style.display = 'none';
    });
}

/**
 * Show elements by selector
 */
function showElements(selector) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        element.style.display = '';
    });
}

/**
 * Check if current user has permission
 */
function hasPermission(permission) {
    const currentUser = userManager.getCurrentUser();
    if (!currentUser) return false;
    return currentUser.permissions.includes(permission);
}

/**
 * Redirect to appropriate section based on permissions
 */
function redirectToAllowedSection() {
    const currentUser = userManager.getCurrentUser();
    if (!currentUser) return;
    
    // Find first allowed section
    if (currentUser.permissions.includes(PERMISSIONS.VIEW_DASHBOARD)) {
        showSection('dashboard');
    } else if (currentUser.permissions.includes(PERMISSIONS.VIEW_PRODUCTS)) {
        showSection('products');
    } else if (currentUser.permissions.includes(PERMISSIONS.VIEW_CUSTOMERS)) {
        showSection('customers');
    } else if (currentUser.permissions.includes(PERMISSIONS.VIEW_SETTINGS)) {
        showSection('settings');
    } else {
        // No permissions - show error or logout
        if (typeof showToast === 'function') {
            showToast('ليس لديك صلاحيات للوصول لأي قسم', 'error');
        }
        setTimeout(() => {
            logoutUser();
        }, 2000);
    }
}

/**
 * Override showSection function to check permissions
 */
const originalShowSection = window.showSection;
window.showSection = function(sectionName) {
    const currentUser = userManager.getCurrentUser();
    if (!currentUser) {
        console.log('⚠️ لا يوجد مستخدم مسجل دخول');
        return;
    }
    
    // Check permissions for each section
    const sectionPermissions = {
        'dashboard': PERMISSIONS.VIEW_DASHBOARD,
        'products': PERMISSIONS.VIEW_PRODUCTS,
        'customers': PERMISSIONS.VIEW_CUSTOMERS,
        'settings': PERMISSIONS.VIEW_SETTINGS
    };
    
    const requiredPermission = sectionPermissions[sectionName];
    if (requiredPermission && !currentUser.permissions.includes(requiredPermission)) {
        if (typeof showToast === 'function') {
            showToast('ليس لديك صلاحية للوصول لهذا القسم', 'error');
        }
        return;
    }
    
    // Call original function if permission is granted
    if (originalShowSection) {
        originalShowSection(sectionName);
    }
};

/**
 * Initialize permissions control
 */
function initializePermissionsControl() {
    // Apply restrictions after a delay to ensure DOM is ready
    setTimeout(() => {
        applyPermissionsRestrictions();
        redirectToAllowedSection();
    }, 1500);
}

/**
 * Update permissions when user changes
 */
function updatePermissionsForUser() {
    applyPermissionsRestrictions();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializePermissionsControl();
    }, 2000);
});

// Export functions for global use
window.applyPermissionsRestrictions = applyPermissionsRestrictions;
window.hasPermission = hasPermission;
window.updatePermissionsForUser = updatePermissionsForUser;

console.log('✅ تم تحميل نظام التحكم في الصلاحيات بنجاح');
