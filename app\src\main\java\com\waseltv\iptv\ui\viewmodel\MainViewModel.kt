package com.waseltv.iptv.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.data.model.ChannelCategory
import com.waseltv.iptv.data.model.ChannelGroup
import com.waseltv.iptv.data.repository.ChannelRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class MainUiState(
    val channels: List<Channel> = emptyList(),
    val channelGroups: List<ChannelGroup> = emptyList(),
    val selectedCategory: ChannelCategory = ChannelCategory.NEWS,
    val isLoading: Boolean = false,
    val error: String? = null,
    val debugInfo: String? = null,
    val userInfo: String? = null,
    val isConnected: Boolean = false
)

class MainViewModel(application: Application) : AndroidViewModel(application) {
    private val channelRepository = ChannelRepository(application)
    
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    fun loadChannels() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null,
                debugInfo = "جاري الاتصال بـ Xtream Codes API..."
            )

            try {
                // اختبار الاتصال أولاً
                val isConnected = channelRepository.testConnection()
                _uiState.value = _uiState.value.copy(
                    isConnected = isConnected,
                    debugInfo = if (isConnected) "✅ تم الاتصال بـ Xtream Codes، جاري تحميل القنوات..."
                               else "❌ فشل الاتصال بـ Xtream Codes، استخدام القنوات الافتراضية..."
                )

                // الحصول على معلومات المستخدم إذا كان متصلاً
                val userInfo = if (isConnected) {
                    try {
                        channelRepository.getUserInfo()
                    } catch (e: Exception) {
                        "خطأ في الحصول على معلومات المستخدم"
                    }
                } else null

                // تحميل القنوات والمجموعات
                val channels = channelRepository.getChannels()
                val channelGroups = channelRepository.getChannelGroups()

                _uiState.value = _uiState.value.copy(
                    channels = channels,
                    channelGroups = channelGroups,
                    isLoading = false,
                    error = null,
                    userInfo = userInfo,
                    debugInfo = "تم تحميل ${channels.size} قناة في ${channelGroups.size} فئات ${if (isConnected) "من Xtream Codes" else "(افتراضية)"}"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "حدث خطأ غير معروف",
                    debugInfo = "خطأ: ${e.message}",
                    isConnected = false
                )
            }
        }
    }
    
    fun refreshChannels() {
        loadChannels()
    }

    fun selectCategory(category: ChannelCategory) {
        _uiState.value = _uiState.value.copy(selectedCategory = category)
    }
}
