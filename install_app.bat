@echo off
echo ========================================
echo      تثبيت تطبيق واصل TV
echo ========================================
echo.

echo 🔍 البحث عن الأجهزة المتصلة...
adb devices

echo.
echo 📱 تثبيت التطبيق...
adb install -r app\build\outputs\apk\debug\app-debug.apk

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم تثبيت التطبيق بنجاح!
    echo.
    echo 🚀 تشغيل التطبيق...
    adb shell am start -n com.waseltv.iptv.debug/com.waseltv.iptv.MainActivity
    echo.
    echo 📱 التطبيق يعمل الآن على الجهاز!
) else (
    echo.
    echo ❌ فشل في تثبيت التطبيق
    echo.
    echo 🔧 تأكد من:
    echo • تفعيل وضع المطور على الجهاز
    echo • تفعيل USB Debugging
    echo • السماح بتثبيت التطبيقات من مصادر غير معروفة
    echo • اتصال الجهاز بالكمبيوتر
)

echo.
pause
