<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    
    <!-- Base application theme -->
    <style name="Theme.DiamondEaglesInventory" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_purple</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/secondary_gold</item>
        <item name="colorSecondaryVariant">@color/secondary_orange</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/primary_blue</item>
        <item name="android:navigationBarColor">@color/primary_blue</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/white</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        
        <!-- Window features -->
        <item name="android:windowBackground">@color/white</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">true</item>
    </style>
    
    <!-- Splash screen theme -->
    <style name="Theme.DiamondEaglesInventory.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/primary_blue</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_diamond_eagle</item>
        <item name="windowSplashScreenAnimationDuration">1000</item>
        <item name="postSplashScreenTheme">@style/Theme.DiamondEaglesInventory</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_blue</item>
        <item name="android:navigationBarColor">@color/primary_blue</item>
    </style>
    
    <!-- Dialog theme -->
    <style name="Theme.DiamondEaglesInventory.Dialog" parent="ThemeOverlay.Material3.Dialog.Alert">
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
    </style>
    
    <!-- Button styles -->
    <style name="Button.Primary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
    <style name="Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/primary_blue</item>
        <item name="android:textColor">@color/primary_blue</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
</resources>
