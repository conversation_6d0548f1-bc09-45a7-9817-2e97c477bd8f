# 📱 دليل تثبيت واختبار تطبيق واصل TV

## 🎯 الهدف
تثبيت تطبيق واصل TV على أجهزة الأندرويد واختباره للتأكد من عمله بشكل صحيح.

---

## 🛠️ المتطلبات

### **للتطوير:**
- ✅ Android Studio مثبت
- ✅ Android SDK (API 24+)
- ✅ Java/Kotlin مثبت
- ✅ ADB (Android Debug Bridge)

### **للاختبار:**
- ✅ جهاز أندرويد (Android 7.0+)
- ✅ كابل USB
- ✅ اتصال إنترنت

---

## 🎨 الخطوة 1: إنشاء الأيقونة

### **1.1 إنشاء أيقونات التطبيق:**
```bash
# افتح أداة إنشاء الأيقونة
start create_app_icon.html
```

### **1.2 تحميل الأيقونات:**
- اضغط "تحميل جميع الأحجام"
- ستحصل على أيقونات بأحجام: 48x48, 72x72, 96x96, 144x144, 192x192

### **1.3 وضع الأيقونات في المشروع:**
```
app/src/main/res/
├── mipmap-mdpi/ic_launcher.png (48x48)
├── mipmap-hdpi/ic_launcher.png (72x72)
├── mipmap-xhdpi/ic_launcher.png (96x96)
├── mipmap-xxhdpi/ic_launcher.png (144x144)
└── mipmap-xxxhdpi/ic_launcher.png (192x192)
```

---

## 🔨 الخطوة 2: بناء التطبيق

### **2.1 بناء APK تلقائياً:**
```bash
# تشغيل سكريبت البناء
build_apk.bat
```

### **2.2 بناء APK يدوياً:**
```bash
# تنظيف المشروع
gradlew clean

# بناء APK للتطوير
gradlew assembleDebug

# بناء APK للإصدار
gradlew assembleRelease
```

### **2.3 مكان ملف APK:**
```
app/build/outputs/apk/debug/app-debug.apk
```

---

## 📱 الخطوة 3: تحضير الجهاز

### **3.1 تفعيل وضع المطور:**
1. اذهب إلى **الإعدادات** > **حول الهاتف**
2. اضغط على **رقم البناء** 7 مرات
3. ستظهر رسالة "أصبحت مطوراً"

### **3.2 تفعيل USB Debugging:**
1. اذهب إلى **الإعدادات** > **خيارات المطور**
2. فعّل **USB Debugging**
3. فعّل **تثبيت عبر USB**

### **3.3 السماح بالمصادر غير المعروفة:**
1. اذهب إلى **الإعدادات** > **الأمان**
2. فعّل **مصادر غير معروفة**

---

## 🚀 الخطوة 4: تثبيت التطبيق

### **4.1 التثبيت التلقائي (مع ADB):**
```bash
# تشغيل سكريبت التثبيت
install_app.bat
```

### **4.2 التثبيت اليدوي:**
```bash
# التحقق من الأجهزة المتصلة
adb devices

# تثبيت التطبيق
adb install -r app/build/outputs/apk/debug/app-debug.apk

# تشغيل التطبيق
adb shell am start -n com.waseltv.iptv.debug/com.waseltv.iptv.MainActivity
```

### **4.3 التثبيت عبر نقل الملف:**
1. انسخ ملف `app-debug.apk` إلى الجهاز
2. افتح مدير الملفات على الجهاز
3. اضغط على ملف APK
4. اضغط "تثبيت"

---

## 🧪 الخطوة 5: اختبار التطبيق

### **5.1 اختبار الواجهة:**
- ✅ التطبيق يفتح بدون أخطاء
- ✅ الأيقونة تظهر بشكل صحيح
- ✅ الأقسام الأربع تظهر: أخبار، رياضة، أطفال، ترفيه
- ✅ عند الضغط على قسم تظهر قنواته

### **5.2 اختبار الاتصال:**
- ✅ التطبيق يتصل بسيرفر واصل TV
- ✅ القنوات تحمّل بنجاح
- ✅ لا توجد رسائل خطأ في الاتصال

### **5.3 اختبار التشغيل:**
- ✅ عند الضغط على قناة يفتح المشغل
- ✅ اختبار URL يعمل بنجاح
- ✅ القنوات التجريبية تعمل
- ✅ ExoPlayer يعرض الفيديو

### **5.4 اختبار الأدوات:**
- ✅ أدوات التشخيص تعمل (🔍)
- ✅ معلومات السيرفر صحيحة
- ✅ اختبار الاتصال ناجح

---

## 🔍 استكشاف الأخطاء

### **مشاكل التثبيت:**
```bash
# إذا فشل التثبيت
adb uninstall com.waseltv.iptv.debug
adb install app/build/outputs/apk/debug/app-debug.apk
```

### **مشاكل الاتصال:**
- تحقق من اتصال الإنترنت
- جرب أدوات الاختبار في المتصفح
- راجع logs في Android Studio

### **مشاكل التشغيل:**
- جرب القنوات التجريبية أولاً
- تحقق من رسائل الخطأ في المشغل
- استخدم أدوات التشخيص

---

## 📊 معلومات التطبيق

### **تفاصيل APK:**
```
اسم التطبيق: واصل TV
Package Name: com.waseltv.iptv.debug
الإصدار: 1.0.0
الحد الأدنى للأندرويد: 7.0 (API 24)
الحد الأقصى للأندرويد: 14 (API 34)
الحجم التقريبي: ~15-20 MB
```

### **الأذونات المطلوبة:**
- ✅ INTERNET - للاتصال بالسيرفر
- ✅ ACCESS_NETWORK_STATE - لفحص حالة الشبكة
- ✅ WAKE_LOCK - لمنع إغلاق الشاشة أثناء التشغيل

---

## 🎯 النتيجة المتوقعة

بعد التثبيت الناجح:
- 📱 أيقونة "واصل TV" تظهر في قائمة التطبيقات
- 🚀 التطبيق يفتح ويعرض 4 أقسام
- 📺 القنوات تحمّل وتعرض بشكل صحيح
- ▶️ تشغيل القنوات يعمل (على الأقل القنوات التجريبية)
- 🔍 أدوات التشخيص تعمل بنجاح

**التطبيق جاهز للاستخدام والاختبار!** 🎉
