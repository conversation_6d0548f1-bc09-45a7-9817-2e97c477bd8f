# دليل تثبيت تطبيق النسور الماسية

## 📱 تثبيت التطبيق على الهاتف

### الطريقة الأولى: تثبيت APK مباشرة

1. **تحميل ملف APK**
   - احصل على ملف `النسور_الماسية_v1.0.apk`
   - انقله إلى هاتفك الأندرويد

2. **تفعيل المصادر غير المعروفة**
   - اذهب إلى **الإعدادات** > **الأمان**
   - فعّل خيار **مصادر غير معروفة** أو **تثبيت التطبيقات من مصادر غير معروفة**

3. **تثبيت التطبيق**
   - اضغط على ملف APK
   - اضغط **تثبيت**
   - انتظر حتى اكتمال التثبيت
   - اضغط **فتح** لتشغيل التطبيق

### الطريقة الثانية: عبر ADB (للمطورين)

```bash
# تأكد من تفعيل وضع المطور و USB Debugging
adb install النسور_الماسية_v1.0.apk
```

## 🛠️ بناء التطبيق من المصدر

### المتطلبات
- **Android Studio** (أحدث إصدار)
- **Java JDK 8+**
- **Android SDK** (API Level 21+)

### خطوات البناء

1. **فتح المشروع**
   ```bash
   # استنساخ أو تحميل المشروع
   cd android_app
   ```

2. **بناء APK**
   ```bash
   # طريقة سريعة
   ./gradlew assembleDebug
   
   # أو استخدم الملف المساعد
   build_apk.bat
   ```

3. **العثور على APK**
   - المسار: `app/build/outputs/apk/debug/app-debug.apk`
   - أو في المجلد الرئيسي: `النسور_الماسية_v1.0.apk`

## ⚙️ إعداد بيئة التطوير

### 1. تثبيت Android Studio
- حمّل من [developer.android.com](https://developer.android.com/studio)
- ثبّت Android SDK
- أضف Android SDK إلى PATH

### 2. إعداد متغيرات البيئة
```bash
# Windows
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools

# Linux/Mac
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

### 3. التحقق من الإعداد
```bash
# فحص Java
java -version

# فحص Android SDK
adb version

# فحص Gradle
./gradlew --version
```

## 🔧 حل المشاكل الشائعة

### مشكلة: "التطبيق غير مثبت"
**الحل:**
- تأكد من تفعيل "مصادر غير معروفة"
- احذف أي إصدار قديم من التطبيق
- أعد تشغيل الهاتف وحاول مرة أخرى

### مشكلة: "مساحة غير كافية"
**الحل:**
- احذف ملفات غير ضرورية
- انقل التطبيقات إلى بطاقة SD
- نظف ذاكرة التخزين المؤقت

### مشكلة: خطأ في البناء
**الحل:**
```bash
# تنظيف المشروع
./gradlew clean

# إعادة بناء
./gradlew assembleDebug

# في حالة مشاكل Gradle
./gradlew wrapper --gradle-version 8.0
```

### مشكلة: "JAVA_HOME غير محدد"
**الحل:**
```bash
# Windows
set JAVA_HOME=C:\Program Files\Java\jdk-11.0.x

# Linux/Mac
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk
```

## 📋 قائمة التحقق قبل التثبيت

- [ ] Android 5.0 أو أحدث
- [ ] 50 MB مساحة فارغة
- [ ] تفعيل "مصادر غير معروفة"
- [ ] إزالة الإصدارات القديمة
- [ ] اتصال بالإنترنت (للتحميل الأولي)

## 🔒 إعدادات الأمان

### أذونات التطبيق
عند التثبيت، سيطلب التطبيق الأذونات التالية:
- **التخزين**: لحفظ البيانات والملفات المصدرة
- **الشبكة**: لتحميل الخطوط والمكتبات
- **الوصول للملفات**: لاستيراد/تصدير البيانات

### حماية البيانات
- جميع البيانات محفوظة محلياً
- لا يتم إرسال بيانات لخوادم خارجية
- النسخ الاحتياطية مشفرة

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تحقق من دليل حل المشاكل** أعلاه
2. **أعد تشغيل الجهاز** وحاول مرة أخرى
3. **تواصل مع الدعم الفني**:
   - البريد الإلكتروني: <EMAIL>
   - الهاتف: +966-XX-XXX-XXXX

## 🔄 التحديثات

### التحقق من التحديثات
- افتح التطبيق
- اذهب إلى **الإعدادات** > **معلومات التطبيق**
- تحقق من رقم الإصدار الحالي

### تثبيت التحديثات
1. حمّل ملف APK الجديد
2. ثبّته فوق الإصدار القديم
3. ستبقى جميع بياناتك محفوظة

---

**ملاحظة مهمة**: احتفظ بنسخة احتياطية من بياناتك قبل أي تحديث أو إعادة تثبيت.
