// Google Drive Sync for Diamond Eagles Inventory - Full Version
// مزامنة Google Drive لتطبيق النسور الماسية - النسخة الكاملة

console.log('🌐 تحميل نظام مزامنة Google Drive الكامل...');

// Google Drive Configuration
const GOOGLE_DRIVE_CONFIG = {
    CLIENT_ID: '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com',
    API_KEY: 'AIzaSyCCEM2W1qq9nVcqD8K2YvYDB6r5sWj9DW8',
    DISCOVERY_DOC: 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest',
    SCOPES: 'https://www.googleapis.com/auth/drive.file'
};

// Global variables
let gapi = null;
let isGoogleDriveReady = false;
let googleUser = null;
let driveAppFolder = null;
let syncInProgress = false;
let autoSyncInterval = null;

// UI State variables
window.isGoogleDriveConnected = false;
window.googleDriveUser = null;

/**
 * تحميل Google API
 * Load Google API
 */
function loadGoogleAPI() {
    return new Promise((resolve, reject) => {
        console.log('📥 تحميل Google API...');

        if (window.gapi) {
            console.log('✅ Google API محمل مسبقاً');
            gapi = window.gapi;
            resolve();
            return;
        }

        // Check if script already exists
        const existingScript = document.querySelector('script[src*="apis.google.com"]');
        if (existingScript) {
            console.log('⏳ Google API قيد التحميل...');
            existingScript.onload = () => {
                gapi = window.gapi;
                console.log('✅ تم تحميل Google API');
                resolve();
            };
            return;
        }

        console.log('🔄 إنشاء script tag لـ Google API...');
        const script = document.createElement('script');
        script.src = 'https://apis.google.com/js/api.js';
        script.async = true;
        script.defer = true;

        script.onload = () => {
            console.log('✅ تم تحميل Google API بنجاح');
            gapi = window.gapi;
            resolve();
        };

        script.onerror = (error) => {
            console.error('❌ فشل في تحميل Google API:', error);
            reject(new Error('فشل في تحميل Google API'));
        };

        document.head.appendChild(script);
        console.log('📤 تم إضافة script tag إلى الصفحة');
    });
}

/**
 * تهيئة Google Drive API
 * Initialize Google Drive API
 */
async function initializeGoogleDrive() {
    try {
        console.log('🔄 تهيئة Google Drive API...');

        // Load Google API
        await loadGoogleAPI();

        // Initialize gapi
        await new Promise((resolve) => {
            gapi.load('auth2', resolve);
        });

        await new Promise((resolve) => {
            gapi.load('client', resolve);
        });

        // Initialize auth
        await gapi.auth2.init({
            client_id: GOOGLE_DRIVE_CONFIG.CLIENT_ID
        });

        // Initialize client
        await gapi.client.init({
            apiKey: GOOGLE_DRIVE_CONFIG.API_KEY,
            clientId: GOOGLE_DRIVE_CONFIG.CLIENT_ID,
            discoveryDocs: [GOOGLE_DRIVE_CONFIG.DISCOVERY_DOC],
            scope: GOOGLE_DRIVE_CONFIG.SCOPES
        });

        console.log('✅ تم تهيئة Google Drive بنجاح');
        isGoogleDriveReady = true;

        // Check if already signed in
        const authInstance = gapi.auth2.getAuthInstance();
        if (authInstance.isSignedIn.get()) {
            googleUser = authInstance.currentUser.get();
            await createAppFolder();

            // Update global state
            window.isGoogleDriveConnected = true;
            window.googleDriveUser = googleUser.getBasicProfile();

            updateGoogleDriveUI(true);
            startAutoSync();

            console.log('🔗 المستخدم مسجل دخول مسبقاً:', googleUser.getBasicProfile().getName());

            // Auto-download data for returning users
            setTimeout(async () => {
                console.log('📥 تحميل البيانات للمستخدم المسجل...');
                await downloadProductsFromDrive();
                await downloadCustomersFromDrive();
            }, 1000);
        }

        return true;

    } catch (error) {
        console.error('❌ فشل في تهيئة Google Drive:', error);
        isGoogleDriveReady = false;
        updateGoogleDriveUI(false);
        return false;
    }
}

/**
 * تسجيل الدخول إلى Google
 * Sign in to Google
 */
async function connectToGoogleDrive() {
    try {
        console.log('🔐 محاولة الاتصال بـ Google Drive...');

        // Check if gapi is available
        if (typeof gapi === 'undefined') {
            console.log('📥 تحميل Google API...');
            await loadGoogleAPI();
        }

        // Initialize if not ready
        if (!isGoogleDriveReady) {
            console.log('🔄 تهيئة Google Drive API...');
            const initialized = await initializeGoogleDrive();
            if (!initialized) {
                throw new Error('فشل في تهيئة Google Drive API');
            }
        }

        // Check if auth instance is available
        if (!gapi.auth2) {
            throw new Error('Google Auth2 غير متاح');
        }

        // Sign in
        console.log('🔑 محاولة تسجيل الدخول...');
        const authInstance = gapi.auth2.getAuthInstance();

        if (!authInstance) {
            throw new Error('Google Auth instance غير متاح');
        }

        googleUser = await authInstance.signIn({
            prompt: 'select_account'
        });

        if (!googleUser) {
            throw new Error('فشل في الحصول على بيانات المستخدم');
        }

        console.log('✅ تم تسجيل الدخول بنجاح');
        console.log('👤 المستخدم:', googleUser.getBasicProfile().getName());

        // Create app folder
        console.log('📁 إنشاء مجلد التطبيق...');
        await createAppFolder();

        // Update UI
        window.isGoogleDriveConnected = true;
        window.googleDriveUser = googleUser.getBasicProfile();
        updateGoogleDriveUI(true);

        // Start auto sync
        startAutoSync();

        // Auto-download existing data after successful connection
        setTimeout(async () => {
            console.log('📥 تحميل البيانات المحفوظة بعد تسجيل الدخول...');
            try {
                await downloadProductsFromDrive();
                await downloadCustomersFromDrive();
                await downloadUsersFromDrive();
                console.log('✅ تم تحميل البيانات المحفوظة');
            } catch (error) {
                console.log('ℹ️ لا توجد بيانات محفوظة مسبقاً:', error.message);
            }
        }, 2000);

        if (typeof showToast === 'function') {
            showToast('تم الاتصال بـ Google Drive بنجاح', 'success');
        }

        return true;

    } catch (error) {
        console.error('❌ فشل في الاتصال بـ Google Drive:', error);

        // Reset connection state
        window.isGoogleDriveConnected = false;
        window.googleDriveUser = null;
        updateGoogleDriveUI(false);

        // Provide detailed error message
        let errorMessage = 'فشل في الاتصال بـ Google Drive';

        if (error.message.includes('popup_blocked')) {
            errorMessage = 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة';
        } else if (error.message.includes('access_denied')) {
            errorMessage = 'تم رفض الوصول. يرجى الموافقة على الصلاحيات المطلوبة';
        } else if (error.message.includes('network')) {
            errorMessage = 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من الاتصال';
        } else if (error.message.includes('Auth')) {
            errorMessage = 'مشكلة في المصادقة. يرجى إعادة المحاولة';
        } else {
            errorMessage += ': ' + error.message;
        }

        console.error('📝 رسالة الخطأ المفصلة:', errorMessage);

        if (typeof showToast === 'function') {
            showToast(errorMessage, 'error', 5000);
        }

        return false;
    }
}

/**
 * إنشاء مجلد التطبيق في Google Drive
 * Create app folder in Google Drive
 */
async function createAppFolder() {
    try {
        console.log('📁 البحث عن مجلد التطبيق...');

        // Check if folder exists
        const response = await gapi.client.drive.files.list({
            q: "name='النسور الماسية' and mimeType='application/vnd.google-apps.folder'",
            spaces: 'drive'
        });

        if (response.result.files.length > 0) {
            driveAppFolder = response.result.files[0];
            console.log('📁 تم العثور على مجلد التطبيق');
        } else {
            // Create folder
            console.log('📁 إنشاء مجلد التطبيق...');
            const folderResponse = await gapi.client.drive.files.create({
                resource: {
                    name: 'النسور الماسية',
                    mimeType: 'application/vnd.google-apps.folder'
                }
            });

            driveAppFolder = folderResponse.result;
            console.log('📁 تم إنشاء مجلد التطبيق بنجاح');
        }

        return driveAppFolder;

    } catch (error) {
        console.error('❌ خطأ في إنشاء المجلد:', error);
        throw error;
    }
}

/**
 * رفع المنتجات إلى Google Drive
 * Upload products to Google Drive
 */
async function uploadProductsToDrive() {
    if (!isGoogleDriveReady || !googleUser || !driveAppFolder) {
        if (typeof showToast === 'function') {
            showToast('يجب تسجيل الدخول إلى Google أولاً', 'warning');
        }
        return false;
    }

    try {
        console.log('📤 رفع المنتجات إلى Google Drive...');
        syncInProgress = true;
        updateSyncUI(true);

        // Get products data
        const productsData = {
            products: typeof products !== 'undefined' ? products : [],
            timestamp: Date.now(),
            userId: googleUser.getBasicProfile().getId(),
            appVersion: '2.0',
            lastModified: new Date().toISOString()
        };

        const fileContent = JSON.stringify(productsData, null, 2);
        const fileName = 'products_data.json';

        // Check if file exists
        const existingFiles = await gapi.client.drive.files.list({
            q: `name='${fileName}' and parents in '${driveAppFolder.id}'`,
            spaces: 'drive'
        });

        let response;
        if (existingFiles.result.files.length > 0) {
            // Update existing file
            const fileId = existingFiles.result.files[0].id;
            response = await fetch(`https://www.googleapis.com/upload/drive/v3/files/${fileId}?uploadType=media`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`,
                    'Content-Type': 'application/json'
                },
                body: fileContent
            });
        } else {
            // Create new file
            const fileMetadata = {
                name: fileName,
                parents: [driveAppFolder.id]
            };

            const form = new FormData();
            form.append('metadata', new Blob([JSON.stringify(fileMetadata)], {type: 'application/json'}));
            form.append('file', new Blob([fileContent], {type: 'application/json'}));

            response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
                },
                body: form
            });
        }

        if (response.ok) {
            console.log('✅ تم رفع المنتجات بنجاح');
            updateLastSyncTime();

            if (typeof showToast === 'function') {
                showToast('تم رفع المنتجات إلى Google Drive بنجاح', 'success');
            }
            return true;
        } else {
            throw new Error('فشل في رفع الملف');
        }

    } catch (error) {
        console.error('❌ خطأ في رفع المنتجات:', error);
        if (typeof showToast === 'function') {
            showToast('فشل في رفع المنتجات: ' + error.message, 'error');
        }
        return false;
    } finally {
        syncInProgress = false;
        updateSyncUI(false);
    }
}

/**
 * تحميل المنتجات من Google Drive
 * Download products from Google Drive
 */
async function downloadProductsFromDrive() {
    if (!isGoogleDriveReady || !googleUser || !driveAppFolder) {
        if (typeof showToast === 'function') {
            showToast('يجب تسجيل الدخول إلى Google أولاً', 'warning');
        }
        return false;
    }

    try {
        console.log('📥 تحميل المنتجات من Google Drive...');
        syncInProgress = true;
        updateSyncUI(true);

        // Find products file
        const response = await gapi.client.drive.files.list({
            q: `name='products_data.json' and parents in '${driveAppFolder.id}'`,
            spaces: 'drive'
        });

        if (response.result.files.length === 0) {
            console.log('📭 لا توجد ملفات منتجات في Google Drive');
            if (typeof showToast === 'function') {
                showToast('لا توجد بيانات منتجات في Google Drive', 'warning');
            }
            return false;
        }

        const file = response.result.files[0];

        // Download file content
        const fileResponse = await gapi.client.drive.files.get({
            fileId: file.id,
            alt: 'media'
        });

        const productsData = JSON.parse(fileResponse.body);

        if (productsData.products && Array.isArray(productsData.products)) {
            // Update global products variable
            if (typeof window.products !== 'undefined') {
                window.products = productsData.products;
            }

            // Save to localStorage
            localStorage.setItem('products', JSON.stringify(productsData.products));

            // Update UI if functions exist
            if (typeof saveProducts === 'function') saveProducts();
            if (typeof displayProducts === 'function') displayProducts();
            if (typeof updateDashboard === 'function') updateDashboard();

            console.log('✅ تم تحميل المنتجات بنجاح');
            updateLastSyncTime();

            if (typeof showToast === 'function') {
                showToast(`تم تحميل ${productsData.products.length} منتج من Google Drive`, 'success');
            }
            return true;
        } else {
            throw new Error('تنسيق ملف غير صحيح');
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل المنتجات:', error);
        if (typeof showToast === 'function') {
            showToast('فشل في تحميل المنتجات: ' + error.message, 'error');
        }
        return false;
    } finally {
        syncInProgress = false;
        updateSyncUI(false);
    }
}

/**
 * رفع العملاء إلى Google Drive
 * Upload customers to Google Drive
 */
async function uploadCustomersToDrive() {
    if (!isGoogleDriveReady || !googleUser || !driveAppFolder) {
        return false;
    }

    try {
        console.log('📤 رفع العملاء إلى Google Drive...');

        const customersData = {
            customers: typeof customers !== 'undefined' ? customers : [],
            timestamp: Date.now(),
            userId: googleUser.getBasicProfile().getId(),
            appVersion: '2.0',
            lastModified: new Date().toISOString()
        };

        const fileContent = JSON.stringify(customersData, null, 2);
        const fileName = 'customers_data.json';

        // Check if file exists and update/create accordingly
        const existingFiles = await gapi.client.drive.files.list({
            q: `name='${fileName}' and parents in '${driveAppFolder.id}'`,
            spaces: 'drive'
        });

        let response;
        if (existingFiles.result.files.length > 0) {
            const fileId = existingFiles.result.files[0].id;
            response = await fetch(`https://www.googleapis.com/upload/drive/v3/files/${fileId}?uploadType=media`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`,
                    'Content-Type': 'application/json'
                },
                body: fileContent
            });
        } else {
            const fileMetadata = {
                name: fileName,
                parents: [driveAppFolder.id]
            };

            const form = new FormData();
            form.append('metadata', new Blob([JSON.stringify(fileMetadata)], {type: 'application/json'}));
            form.append('file', new Blob([fileContent], {type: 'application/json'}));

            response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
                },
                body: form
            });
        }

        if (response.ok) {
            console.log('✅ تم رفع العملاء بنجاح');
            return true;
        }

        return false;

    } catch (error) {
        console.error('❌ خطأ في رفع العملاء:', error);
        return false;
    }
}

/**
 * تحميل العملاء من Google Drive
 * Download customers from Google Drive
 */
async function downloadCustomersFromDrive() {
    if (!isGoogleDriveReady || !googleUser || !driveAppFolder) {
        return false;
    }

    try {
        console.log('📥 تحميل العملاء من Google Drive...');

        // Find customers file
        const response = await gapi.client.drive.files.list({
            q: `name='customers_data.json' and parents in '${driveAppFolder.id}'`,
            spaces: 'drive'
        });

        if (response.result.files.length === 0) {
            console.log('📭 لا توجد ملفات عملاء في Google Drive');
            return false;
        }

        const file = response.result.files[0];

        // Download file content
        const fileResponse = await gapi.client.drive.files.get({
            fileId: file.id,
            alt: 'media'
        });

        const customersData = JSON.parse(fileResponse.body);

        if (customersData.customers && Array.isArray(customersData.customers)) {
            // Update global customers variable
            if (typeof window.customers !== 'undefined') {
                window.customers = customersData.customers;
            }

            // Save to localStorage
            localStorage.setItem('customers', JSON.stringify(customersData.customers));

            // Update UI if functions exist
            if (typeof saveCustomers === 'function') saveCustomers();
            if (typeof displayCustomers === 'function') displayCustomers();
            if (typeof updateDashboard === 'function') updateDashboard();

            console.log('✅ تم تحميل العملاء بنجاح');

            if (typeof showToast === 'function') {
                showToast(`تم تحميل ${customersData.customers.length} عميل من Google Drive`, 'success');
            }
            return true;
        } else {
            throw new Error('تنسيق ملف العملاء غير صحيح');
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل العملاء:', error);
        return false;
    }
}

/**
 * مزامنة شاملة
 * Full sync
 */
async function uploadAllToGoogleDrive() {
    if (!isGoogleDriveReady || !googleUser) {
        if (typeof showToast === 'function') {
            showToast('يجب تسجيل الدخول إلى Google أولاً', 'warning');
        }
        return;
    }

    try {
        console.log('🔄 بدء المزامنة الشاملة...');

        // Upload products
        await uploadProductsToDrive();

        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 500));

        // Upload customers
        await uploadCustomersToDrive();

        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 500));

        // Upload users
        await uploadUsersToDrive();

        if (typeof showToast === 'function') {
            showToast('تمت المزامنة الشاملة بنجاح (المنتجات + العملاء + المستخدمين)', 'success');
        }

    } catch (error) {
        console.error('❌ خطأ في المزامنة الشاملة:', error);
        if (typeof showToast === 'function') {
            showToast('فشل في المزامنة الشاملة', 'error');
        }
    }
}

/**
 * تحميل جميع البيانات
 * Download all data
 */
async function downloadAllFromGoogleDrive() {
    if (!isGoogleDriveReady || !googleUser) {
        if (typeof showToast === 'function') {
            showToast('يجب تسجيل الدخول إلى Google أولاً', 'warning');
        }
        return;
    }

    try {
        console.log('📥 تحميل جميع البيانات...');

        let productsLoaded = false;
        let customersLoaded = false;
        let usersLoaded = false;

        // Download products
        try {
            productsLoaded = await downloadProductsFromDrive();
        } catch (error) {
            console.error('خطأ في تحميل المنتجات:', error);
        }

        // Download customers
        try {
            customersLoaded = await downloadCustomersFromDrive();
        } catch (error) {
            console.error('خطأ في تحميل العملاء:', error);
        }

        // Download users
        try {
            usersLoaded = await downloadUsersFromDrive();
        } catch (error) {
            console.error('خطأ في تحميل المستخدمين:', error);
        }

        // Show appropriate message
        if (productsLoaded || customersLoaded || usersLoaded) {
            let message = 'تم تحميل البيانات المتاحة: ';
            const loaded = [];
            if (productsLoaded) loaded.push('المنتجات');
            if (customersLoaded) loaded.push('العملاء');
            if (usersLoaded) loaded.push('المستخدمين');
            message += loaded.join(' + ');

            if (typeof showToast === 'function') {
                showToast(message, 'success');
            }

            // Reload tables if they exist
            if (typeof loadProducts === 'function') {
                loadProducts();
            }
            if (typeof loadCustomers === 'function') {
                loadCustomers();
            }
        } else {
            if (typeof showToast === 'function') {
                showToast('لا توجد بيانات محفوظة في Google Drive', 'warning');
            }
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        if (typeof showToast === 'function') {
            showToast('فشل في تحميل البيانات', 'error');
        }
    }
}

/**
 * بدء المزامنة التلقائية
 * Start auto sync
 */
function startAutoSync() {
    if (autoSyncInterval) {
        clearInterval(autoSyncInterval);
    }

    console.log('⏰ بدء المزامنة التلقائية كل 30 ثانية...');

    autoSyncInterval = setInterval(async () => {
        if (isGoogleDriveReady && googleUser && !syncInProgress) {
            console.log('🔄 مزامنة تلقائية...');
            await uploadProductsToDrive();
            await uploadCustomersToDrive();
        }
    }, 30000); // كل 30 ثانية
}

/**
 * إيقاف المزامنة التلقائية
 * Stop auto sync
 */
function stopAutoSync() {
    if (autoSyncInterval) {
        clearInterval(autoSyncInterval);
        autoSyncInterval = null;
        console.log('⏹️ تم إيقاف المزامنة التلقائية');
    }
}

/**
 * تحديث واجهة المستخدم
 * Update UI
 */
function updateGoogleDriveUI(connected) {
    window.isGoogleDriveConnected = connected;

    const statusElement = document.getElementById('googleDriveStatus');
    const userElement = document.getElementById('googleDriveUser');
    const connectBtn = document.getElementById('googleDriveConnectBtn');
    const uploadBtn = document.getElementById('googleDriveUploadBtn');
    const downloadBtn = document.getElementById('googleDriveDownloadBtn');
    const uploadUsersBtn = document.getElementById('uploadUsersBtn');
    const downloadUsersBtn = document.getElementById('downloadUsersBtn');

    if (connected && googleUser) {
        // Update status
        if (statusElement) {
            statusElement.innerHTML = '<span class="status-text connected">متصل</span>';
        }

        // Update user info
        if (userElement) {
            userElement.textContent = googleUser.getBasicProfile().getName();
        }

        // Update buttons
        if (connectBtn) {
            connectBtn.innerHTML = '<i class="fas fa-check"></i> متصل بـ Google';
            connectBtn.disabled = true;
        }

        if (uploadBtn) uploadBtn.disabled = false;
        if (downloadBtn) downloadBtn.disabled = false;
        if (uploadUsersBtn) uploadUsersBtn.disabled = false;
        if (downloadUsersBtn) downloadUsersBtn.disabled = false;

    } else {
        // Update status
        if (statusElement) {
            statusElement.innerHTML = '<span class="status-text offline">غير متصل</span>';
        }

        // Update user info
        if (userElement) {
            userElement.textContent = 'غير متصل';
        }

        // Update buttons
        if (connectBtn) {
            connectBtn.innerHTML = '<i class="fab fa-google"></i> تسجيل دخول Google';
            connectBtn.disabled = false;
        }

        if (uploadBtn) uploadBtn.disabled = true;
        if (downloadBtn) downloadBtn.disabled = true;
        if (uploadUsersBtn) uploadUsersBtn.disabled = true;
        if (downloadUsersBtn) downloadUsersBtn.disabled = true;
    }
}

/**
 * تحديث واجهة المزامنة
 * Update sync UI
 */
function updateSyncUI(syncing) {
    const syncButtons = document.querySelectorAll('[id*="Upload"], [id*="Download"]');
    syncButtons.forEach(btn => {
        if (btn) {
            btn.disabled = syncing;
            if (syncing) {
                btn.innerHTML = btn.innerHTML.replace(/fas fa-\w+/, 'fas fa-spinner fa-spin');
            }
        }
    });
}

/**
 * تحديث وقت آخر مزامنة
 * Update last sync time
 */
function updateLastSyncTime() {
    const syncElement = document.getElementById('lastGoogleDriveSync');
    if (syncElement) {
        const now = new Date().toLocaleString('ar-SA');
        syncElement.textContent = now;
        localStorage.setItem('lastGoogleDriveSync', now);
    }
}

/**
 * تهيئة عند تحميل الصفحة
 * Initialize on page load
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 تهيئة نظام Google Drive...');

    // Load last sync time
    const lastSync = localStorage.getItem('lastGoogleDriveSync');
    if (lastSync) {
        const syncElement = document.getElementById('lastGoogleDriveSync');
        if (syncElement) {
            syncElement.textContent = lastSync;
        }
    }

    // Initialize Google Drive after a delay
    setTimeout(async () => {
        await initializeGoogleDrive();
    }, 2000);
});

/**
 * فحص اتصال Google Drive
 * Check Google Drive connection
 */
async function checkGoogleDriveConnection() {
    console.log('🔍 فحص اتصال Google Drive...');

    try {
        if (!isGoogleDriveReady) {
            console.log('⚠️ Google Drive API غير مهيأ');
            if (typeof showToast === 'function') {
                showToast('Google Drive API غير مهيأ', 'warning');
            }
            return false;
        }

        if (!googleUser) {
            console.log('⚠️ المستخدم غير مسجل دخول');
            if (typeof showToast === 'function') {
                showToast('يجب تسجيل الدخول إلى Google أولاً', 'warning');
            }
            return false;
        }

        // Test API call
        const response = await gapi.client.drive.about.get({
            fields: 'user'
        });

        if (response.status === 200) {
            console.log('✅ اتصال Google Drive يعمل بشكل صحيح');
            console.log('👤 المستخدم:', response.result.user.displayName);

            if (typeof showToast === 'function') {
                showToast(`متصل بـ Google Drive - ${response.result.user.displayName}`, 'success');
            }

            // Update UI
            updateGoogleDriveUI();

            return true;
        } else {
            throw new Error('فشل في الاتصال');
        }

    } catch (error) {
        console.error('❌ خطأ في فحص اتصال Google Drive:', error);

        if (typeof showToast === 'function') {
            showToast('فشل في الاتصال بـ Google Drive', 'error');
        }

        // Reset connection state
        window.isGoogleDriveConnected = false;
        updateGoogleDriveUI();

        return false;
    }
}

/**
 * مزامنة يدوية فورية
 * Manual immediate sync
 */
async function performManualSync() {
    console.log('🔄 بدء المزامنة اليدوية...');

    if (!isGoogleDriveReady || !googleUser) {
        if (typeof showToast === 'function') {
            showToast('يجب تسجيل الدخول إلى Google أولاً', 'warning');
        }
        return;
    }

    if (syncInProgress) {
        if (typeof showToast === 'function') {
            showToast('المزامنة قيد التنفيذ بالفعل', 'info');
        }
        return;
    }

    try {
        syncInProgress = true;

        // Update button state
        const syncBtn = document.querySelector('[onclick="performManualSync()"]');
        if (syncBtn) {
            syncBtn.disabled = true;
            syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المزامنة...';
        }

        if (typeof showToast === 'function') {
            showToast('بدء المزامنة اليدوية...', 'info');
        }

        // Upload current data
        await uploadAllToGoogleDrive();

        // Download latest data
        await downloadAllFromGoogleDrive();

        // Update last sync time
        localStorage.setItem('lastGoogleDriveSync', new Date().toISOString());
        updateLastSyncDisplay();

        console.log('✅ تمت المزامنة اليدوية بنجاح');

        if (typeof showToast === 'function') {
            showToast('تمت المزامنة بنجاح', 'success');
        }

    } catch (error) {
        console.error('❌ خطأ في المزامنة اليدوية:', error);

        if (typeof showToast === 'function') {
            showToast('فشل في المزامنة: ' + error.message, 'error');
        }
    } finally {
        syncInProgress = false;

        // Reset button state
        const syncBtn = document.querySelector('[onclick="performManualSync()"]');
        if (syncBtn) {
            syncBtn.disabled = false;
            syncBtn.innerHTML = '<i class="fas fa-sync-alt"></i> مزامنة يدوية فورية';
        }
    }
}

/**
 * تبديل المزامنة التلقائية
 * Toggle auto sync
 */
function toggleAutoSync() {
    const autoSyncCheckbox = document.getElementById('autoSyncEnabled');
    if (!autoSyncCheckbox) return;

    const isEnabled = autoSyncCheckbox.checked;

    if (isEnabled) {
        console.log('✅ تم تفعيل المزامنة التلقائية');
        if (typeof showToast === 'function') {
            showToast('تم تفعيل المزامنة التلقائية', 'success');
        }

        // Start auto sync if connected
        if (isGoogleDriveReady && googleUser) {
            startAutoSync();
        }
    } else {
        console.log('⏹️ تم إيقاف المزامنة التلقائية');
        if (typeof showToast === 'function') {
            showToast('تم إيقاف المزامنة التلقائية', 'info');
        }

        // Stop auto sync
        stopAutoSync();
    }

    // Save preference
    localStorage.setItem('autoSyncEnabled', isEnabled.toString());
}

/**
 * رفع بيانات المستخدمين إلى Google Drive
 * Upload users data to Google Drive
 */
async function uploadUsersToDrive() {
    if (!isGoogleDriveReady || !googleUser || !driveAppFolder) {
        return false;
    }

    try {
        console.log('📤 رفع بيانات المستخدمين إلى Google Drive...');

        // Get users data from localStorage
        const usersData = localStorage.getItem('systemUsers');
        if (!usersData) {
            console.log('📭 لا توجد بيانات مستخدمين للرفع');
            return false;
        }

        const users = JSON.parse(usersData);
        console.log(`📊 رفع ${users.length} مستخدم...`);

        const fileName = 'users_data.json';
        const fileContent = JSON.stringify({
            users: users,
            lastUpdated: new Date().toISOString(),
            version: '1.0'
        }, null, 2);

        // Check if file exists
        const existingFiles = await gapi.client.drive.files.list({
            q: `name='${fileName}' and parents in '${driveAppFolder.id}'`,
            spaces: 'drive'
        });

        let response;
        if (existingFiles.result.files.length > 0) {
            // Update existing file
            const fileId = existingFiles.result.files[0].id;
            response = await fetch(`https://www.googleapis.com/upload/drive/v3/files/${fileId}?uploadType=media`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`,
                    'Content-Type': 'application/json'
                },
                body: fileContent
            });
        } else {
            // Create new file
            const fileMetadata = {
                name: fileName,
                parents: [driveAppFolder.id]
            };

            const form = new FormData();
            form.append('metadata', new Blob([JSON.stringify(fileMetadata)], {type: 'application/json'}));
            form.append('file', new Blob([fileContent], {type: 'application/json'}));

            response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
                },
                body: form
            });
        }

        if (response.ok) {
            console.log('✅ تم رفع بيانات المستخدمين بنجاح');
            updateLastSyncTime();
            return true;
        } else {
            throw new Error('فشل في رفع الملف');
        }

    } catch (error) {
        console.error('❌ خطأ في رفع بيانات المستخدمين:', error);
        return false;
    }
}

/**
 * تحميل بيانات المستخدمين من Google Drive
 * Download users data from Google Drive
 */
async function downloadUsersFromDrive() {
    if (!isGoogleDriveReady || !googleUser || !driveAppFolder) {
        return false;
    }

    try {
        console.log('📥 تحميل بيانات المستخدمين من Google Drive...');

        // Find users file
        const response = await gapi.client.drive.files.list({
            q: `name='users_data.json' and parents in '${driveAppFolder.id}'`,
            spaces: 'drive'
        });

        if (response.result.files.length === 0) {
            console.log('📭 لا توجد ملفات مستخدمين في Google Drive');
            return false;
        }

        const file = response.result.files[0];

        // Download file content
        const fileResponse = await gapi.client.drive.files.get({
            fileId: file.id,
            alt: 'media'
        });

        const usersData = JSON.parse(fileResponse.body);

        if (usersData.users && Array.isArray(usersData.users)) {
            // Save to localStorage
            localStorage.setItem('systemUsers', JSON.stringify(usersData.users));

            console.log(`✅ تم تحميل ${usersData.users.length} مستخدم من Google Drive`);
            console.log('📅 آخر تحديث:', usersData.lastUpdated);

            // Reload user management if available
            if (typeof userManager !== 'undefined' && typeof userManager.loadUsers === 'function') {
                userManager.loadUsers();
            }

            return true;
        } else {
            throw new Error('تنسيق ملف المستخدمين غير صحيح');
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات المستخدمين:', error);
        return false;
    }
}

// Export functions to global scope for button access
window.connectToGoogleDrive = connectToGoogleDrive;
window.uploadAllToGoogleDrive = uploadAllToGoogleDrive;
window.downloadAllFromGoogleDrive = downloadAllFromGoogleDrive;
window.uploadUsersToDrive = uploadUsersToDrive;
window.downloadUsersFromDrive = downloadUsersFromDrive;
window.checkGoogleDriveConnection = checkGoogleDriveConnection;
window.performManualSync = performManualSync;
window.toggleAutoSync = toggleAutoSync;
window.updateGoogleDriveUI = updateGoogleDriveUI;
window.initializeGoogleDriveSync = initializeGoogleDriveSync;

console.log('✅ تم تحميل نظام Google Drive الكامل بنجاح');
console.log('🔗 تم تصدير جميع الوظائف للاستخدام العام');
