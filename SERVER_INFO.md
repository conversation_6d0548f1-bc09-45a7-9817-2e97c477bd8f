# 📡 معلومات سيرفر واصل TV

## 📋 معلومات الاتصال

### **قائمة التشغيل**
```
اسم قائمة التشغيل: واصل TV
```

### **معلومات السيرفر**
```
رابط السيرفر: http://maventv.one:80
اسم المستخدم: odaitv
كلمة المرور: Odai2030
نوع السيرفر: Xtream Codes IPTV API
```

### **حالة الاتصال**
```
✅ متصل تلقائياً
✅ مدمج في التطبيق
✅ لا حاجة لإعدادات إضافية
```

---

## 🔗 روابط API المستخدمة

### **1. معلومات المستخدم**
```
GET http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030
```

### **2. قائمة القنوات المباشرة**
```
GET http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030&action=get_live_streams
```

### **3. فئات القنوات**
```
GET http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030&action=get_live_categories
```

---

## 📺 تنسيق Stream URLs

### **التنسيق الأساسي**
```
http://maventv.one:80/live/odaitv/Odai2030/[STREAM_ID].ts
```

### **تنسيقات بديلة**
```
http://maventv.one:80/live/odaitv/Odai2030/[STREAM_ID].m3u8
http://maventv.one:80/hls/odaitv/Odai2030/[STREAM_ID].m3u8
http://maventv.one:80/streaming/odaitv/Odai2030/[STREAM_ID].ts
```

---

## 🎯 أمثلة على Stream IDs

### **📰 قنوات الأخبار**
```
106997 - القرآن الكريم
106998 - الجزيرة مباشر
106999 - العربية
107000 - BBC Arabic
107001 - سكاي نيوز عربية
107002 - الحدث
```

### **⚽ قنوات الرياضة**
```
107010 - بي إن سبورت 1 HD
107011 - بي إن سبورت 2 HD
107012 - بي إن سبورت 3 HD
107013 - أبو ظبي الرياضية
107014 - الكأس
```

### **🧸 قنوات الأطفال**
```
107020 - سبيستون
107021 - طيور الجنة
107022 - براعم
107023 - كرتون نتورك بالعربية
107024 - ديزني جونيور
```

### **🎬 قنوات الترفيه**
```
107030 - MBC 1 HD
107031 - MBC 2 HD
107032 - MBC 4 HD
107033 - دبي
107034 - الحياة
```

---

## 🛠️ الملفات المحدثة

### **ملفات التطبيق**
- ✅ `XtreamCodesApi.kt` - API الرئيسي
- ✅ `DiagnosticsViewModel.kt` - أدوات التشخيص
- ✅ `DiagnosticsActivity.kt` - واجهة التشخيص
- ✅ `strings.xml` - النصوص والعناوين

### **أدوات الاختبار**
- ✅ `test_connection.html` - اختبار الاتصال
- ✅ `test_formats.html` - اختبار التنسيقات
- ✅ `test_stream_direct.html` - اختبار التشغيل المباشر
- ✅ `test_categories.html` - اختبار الأقسام
- ✅ `app_demo.html` - العرض التقديمي

### **الوثائق**
- ✅ `README.md` - الدليل الرئيسي
- ✅ `SERVER_INFO.md` - معلومات السيرفر (هذا الملف)

---

## 🔍 اختبار الاتصال

### **اختبار سريع**
```bash
curl "http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030"
```

### **اختبار قائمة القنوات**
```bash
curl "http://maventv.one:80/player_api.php?username=odaitv&password=Odai2030&action=get_live_streams"
```

### **اختبار stream محدد**
```bash
curl -I "http://maventv.one:80/live/odaitv/Odai2030/106997.ts"
```

---

## 📱 حالة التطبيق

### **✅ جاهز للاستخدام**
- معلومات السيرفر محدثة ومتسقة
- جميع الملفات تستخدم نفس المعلومات
- أدوات الاختبار جاهزة
- التطبيق متصل بسيرفر واصل TV

### **🎯 الأقسام المتاحة**
- 📰 أخبار
- ⚽ رياضة  
- 🧸 أطفال
- 🎬 ترفيه

**التطبيق جاهز للاستخدام مع سيرفر واصل TV!** 📺✨
