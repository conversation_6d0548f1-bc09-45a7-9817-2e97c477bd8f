@echo off
title إنشاء APK - واصل TV
color 0A

echo.
echo ████████████████████████████████████████
echo          إنشاء APK - واصل TV
echo ████████████████████████████████████████
echo.

echo 🚀 بدء إنشاء ملف APK...
echo.

REM تنظيف المشروع
echo [1/3] 🧹 تنظيف المشروع...
call gradlew clean >nul 2>&1

REM بناء APK
echo [2/3] 🔨 بناء ملف APK...
call gradlew assembleDebug

REM التحقق من النجاح
echo [3/3] ✅ التحقق من النتيجة...
echo.

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ████████████████████████████████████████
    echo              🎉 نجح الإنشاء!
    echo ████████████████████████████████████████
    echo.
    echo 📱 ملف APK جاهز للتثبيت:
    echo 📍 %CD%\app\build\outputs\apk\debug\app-debug.apk
    echo.
    
    REM عرض معلومات الملف
    echo 📋 معلومات الملف:
    for %%A in ("app\build\outputs\apk\debug\app-debug.apk") do (
        echo    📏 الحجم: %%~zA bytes
        echo    📅 التاريخ: %%~tA
    )
    echo    📦 النوع: Android Application Package
    echo    🏷️ الاسم: واصل TV
    echo    🔢 الإصدار: 1.0.0
    echo.
    
    echo 🔥 فتح مجلد الملف...
    explorer "%CD%\app\build\outputs\apk\debug"
    
    echo.
    echo 📱 خطوات التثبيت:
    echo    1. انسخ ملف app-debug.apk إلى جهاز الأندرويد
    echo    2. افتح مدير الملفات على الجهاز
    echo    3. اضغط على ملف APK
    echo    4. اضغط "تثبيت"
    echo.
    echo 🎯 أو استخدم ADB:
    echo    adb install "%CD%\app\build\outputs\apk\debug\app-debug.apk"
    
) else (
    echo ████████████████████████████████████████
    echo              ❌ فشل الإنشاء!
    echo ████████████████████████████████████████
    echo.
    echo 🔧 تحقق من:
    echo    • تثبيت Android Studio
    echo    • تثبيت Java JDK
    echo    • متغيرات البيئة ANDROID_HOME
    echo    • اتصال الإنترنت لتحميل Dependencies
    echo.
    echo 📞 راجع رسائل الخطأ أعلاه لمزيد من التفاصيل
)

echo.
echo ████████████████████████████████████████
pause
