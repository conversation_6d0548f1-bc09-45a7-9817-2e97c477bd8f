plugins {
    id 'com.android.application'
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.diamondeagles.inventory'
    compileSdk 34

    defaultConfig {
        applicationId "com.diamondeagles.inventory"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.webkit:webkit:1.8.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'androidx.core:core-splashscreen:1.0.1'

    // Firebase BOM - يدير إصدارات جميع مكتبات Firebase
    implementation platform('com.google.firebase:firebase-bom:32.7.0')

    // Firebase Analytics
    implementation 'com.google.firebase:firebase-analytics'

    // Firebase Firestore - قاعدة البيانات السحابية
    implementation 'com.google.firebase:firebase-firestore'

    // Firebase Authentication - نظام المصادقة
    implementation 'com.google.firebase:firebase-auth'

    // Firebase Storage - تخزين الملفات
    implementation 'com.google.firebase:firebase-storage'

    // Firebase Cloud Messaging - الإشعارات
    implementation 'com.google.firebase:firebase-messaging'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
