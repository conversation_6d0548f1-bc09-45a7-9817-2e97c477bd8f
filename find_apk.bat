@echo off
echo ========================================
echo      البحث عن ملف APK - واصل TV
echo ========================================
echo.

echo 📂 المسار الحالي: %CD%
echo.

echo 🔍 البحث عن ملفات APK...
echo.

REM البحث في المسار المتوقع
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✅ تم العثور على APK في المسار المتوقع:
    echo 📍 %CD%\app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 📋 معلومات الملف:
    dir "app\build\outputs\apk\debug\app-debug.apk"
    echo.
    echo 📁 فتح المجلد:
    explorer "%CD%\app\build\outputs\apk\debug"
    goto :found
)

REM البحث في مجلد app\build
if exist "app\build" (
    echo 📁 مجلد build موجود، البحث عن ملفات APK...
    for /r "app\build" %%f in (*.apk) do (
        echo ✅ تم العثور على: %%f
        set "apk_found=1"
    )
    if defined apk_found goto :found
)

REM البحث في جميع المجلدات
echo 🔍 البحث في جميع المجلدات...
for /r . %%f in (*.apk) do (
    echo ✅ تم العثور على APK: %%f
    set "apk_found=1"
)

if defined apk_found (
    goto :found
) else (
    goto :notfound
)

:found
echo.
echo 🎉 تم العثور على ملف APK!
echo.
echo 📱 لتثبيت التطبيق:
echo 1. انسخ ملف APK إلى جهاز الأندرويد
echo 2. افتح مدير الملفات على الجهاز
echo 3. اضغط على ملف APK
echo 4. اضغط "تثبيت"
echo.
echo 🔧 أو استخدم ADB:
echo adb install "مسار_الملف.apk"
goto :end

:notfound
echo.
echo ❌ لم يتم العثور على ملف APK!
echo.
echo 🔧 لبناء التطبيق:
echo 1. تشغيل: build_apk.bat
echo 2. أو يدوياً: gradlew assembleDebug
echo.
echo 📍 سيتم إنشاء الملف في:
echo %CD%\app\build\outputs\apk\debug\app-debug.apk
echo.

:end
echo.
pause
