package com.waseltv.iptv.ui.diagnostics

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.waseltv.iptv.data.model.Channel
import com.waseltv.iptv.ui.theme.WASELTVTheme
import kotlinx.coroutines.launch

class DiagnosticsActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            WASELTVTheme {
                DiagnosticsScreen(
                    onBackPressed = { finish() }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DiagnosticsScreen(
    onBackPressed: () -> Unit,
    viewModel: DiagnosticsViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val scope = rememberCoroutineScope()
    
    LaunchedEffect(Unit) {
        viewModel.loadTestChannels()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("🔍 تشخيص الـ Streams") },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "رجوع")
                    }
                },
                actions = {
                    IconButton(
                        onClick = {
                            scope.launch {
                                viewModel.testAllStreams()
                            }
                        }
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = "اختبار الكل")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // معلومات السيرفر
            item {
                ServerInfoCard()
            }
            
            // قائمة القنوات للاختبار
            items(uiState.testChannels) { channel ->
                StreamTestCard(
                    channel = channel,
                    testResult = uiState.testResults[channel.id],
                    isLoading = uiState.loadingChannels.contains(channel.id),
                    onTest = { 
                        scope.launch {
                            viewModel.testStream(channel)
                        }
                    }
                )
            }
            
            // ملخص النتائج
            if (uiState.testResults.isNotEmpty()) {
                item {
                    TestSummaryCard(uiState.testResults)
                }
            }
        }
    }
}

@Composable
fun ServerInfoCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.primaryContainer)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "📡 معلومات سيرفر واصل TV",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "قائمة التشغيل: واصل TV",
                style = MaterialTheme.typography.bodyMedium,
                fontFamily = FontFamily.Monospace
            )
            Text(
                text = "السيرفر: maventv.one:80",
                style = MaterialTheme.typography.bodyMedium,
                fontFamily = FontFamily.Monospace
            )
            Text(
                text = "المستخدم: odaitv",
                style = MaterialTheme.typography.bodyMedium,
                fontFamily = FontFamily.Monospace
            )
            Text(
                text = "النوع: Xtream Codes IPTV",
                style = MaterialTheme.typography.bodyMedium,
                fontFamily = FontFamily.Monospace
            )
        }
    }
}

@Composable
fun StreamTestCard(
    channel: Channel,
    testResult: StreamTestResult?,
    isLoading: Boolean,
    onTest: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = channel.name,
                        style = MaterialTheme.typography.titleSmall
                    )
                    Text(
                        text = "ID: ${channel.id} • ${channel.group}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                if (isLoading) {
                    CircularProgressIndicator(modifier = Modifier.size(24.dp))
                } else {
                    IconButton(onClick = onTest) {
                        Icon(Icons.Default.PlayArrow, contentDescription = "اختبار")
                    }
                }
            }
            
            // عرض URL
            Text(
                text = channel.url,
                style = MaterialTheme.typography.bodySmall,
                fontFamily = FontFamily.Monospace,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 4.dp)
            )
            
            // عرض نتيجة الاختبار
            testResult?.let { result ->
                Spacer(modifier = Modifier.height(8.dp))
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = if (result.isSuccess) 
                            Color.Green.copy(alpha = 0.1f) 
                        else 
                            Color.Red.copy(alpha = 0.1f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = if (result.isSuccess) "✅ يعمل" else "❌ لا يعمل",
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (result.isSuccess) Color.Green else Color.Red
                        )
                        if (result.message.isNotEmpty()) {
                            Text(
                                text = result.message,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Text(
                            text = "وقت الاختبار: ${result.duration}ms",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TestSummaryCard(testResults: Map<String, StreamTestResult>) {
    val successCount = testResults.values.count { it.isSuccess }
    val totalCount = testResults.size
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "📊 ملخص النتائج",
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "✅ يعمل: $successCount من $totalCount",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Green
            )
            Text(
                text = "❌ لا يعمل: ${totalCount - successCount} من $totalCount",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Red
            )
            
            val successRate = if (totalCount > 0) (successCount * 100) / totalCount else 0
            Text(
                text = "معدل النجاح: $successRate%",
                style = MaterialTheme.typography.bodyMedium,
                color = if (successRate > 50) Color.Green else Color.Red
            )
        }
    }
}

data class StreamTestResult(
    val isSuccess: Boolean,
    val message: String,
    val duration: Long
)
