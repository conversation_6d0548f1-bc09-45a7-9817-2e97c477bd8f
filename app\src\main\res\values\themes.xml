<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.WASELTV" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_blue</item>
        <item name="colorSecondaryVariant">@color/accent_blue_dark</item>
        <item name="colorOnSecondary">@color/text_white</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_white</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_blue_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Action bar -->
        <item name="colorPrimaryDark">@color/primary_blue_dark</item>
    </style>
    
    <!-- Fullscreen theme for video player -->
    <style name="Theme.WASELTV.Fullscreen" parent="Theme.WASELTV">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@color/player_background</item>
        <item name="android:statusBarColor">@color/player_background</item>
        <item name="android:navigationBarColor">@color/player_background</item>
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="p">shortEdges</item>
    </style>
    
    <!-- Card style -->
    <style name="CardViewStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    
    <!-- Button styles -->
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/primary_blue</item>
        <item name="android:textColor">@color/primary_blue</item>
        <item name="cornerRadius">8dp</item>
    </style>
</resources>
