// Firebase Configuration for Web
// تكوين Firebase للويب

// Firebase SDK imports
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { getFirestore, collection, doc, setDoc, getDoc, connectFirestoreEmulator } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
import { getAuth, signInAnonymously, connectAuthEmulator } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Firebase configuration
// استبدل هذا بالتكوين الحقيقي من Firebase Console
const firebaseConfig = {
  // 👇 الصق التكوين الخاص بك هنا من Firebase Console
  apiKey: "YOUR_API_KEY_HERE",
  authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_PROJECT_ID.appspot.com",
  messagingSenderId: "YOUR_SENDER_ID",
  appId: "YOUR_APP_ID"
  // 👆 استبدل القيم أعلاه بالقيم الحقيقية من Firebase
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Web Firebase Manager
class WebFirebaseManager {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
        this.init();
    }

    async init() {
        try {
            // Sign in anonymously
            const userCredential = await signInAnonymously(auth);
            this.currentUser = userCredential.user;
            this.isInitialized = true;
            
            console.log('🔥 Firebase initialized for web');
            console.log('👤 User ID:', this.currentUser.uid);
            
            // Update UI
            if (typeof updateCloudUI === 'function') {
                isCloudEnabled = true;
                cloudUserId = this.currentUser.uid;
                updateCloudUI();
                showCloudStatus('متصل بالسحابة', 'success');
            }
            
        } catch (error) {
            console.error('❌ Firebase initialization failed:', error);
            this.isInitialized = false;
            
            if (typeof updateCloudUI === 'function') {
                isCloudEnabled = false;
                updateCloudUI();
                showCloudStatus('فشل الاتصال بالسحابة', 'error');
            }
        }
    }

    async uploadProducts(productsData) {
        if (!this.isInitialized) {
            throw new Error('Firebase not initialized');
        }

        try {
            const docRef = doc(db, 'products', 'products_data');
            await setDoc(docRef, {
                products: JSON.stringify(productsData),
                timestamp: Date.now(),
                userId: this.currentUser.uid
            });
            
            console.log('✅ Products uploaded successfully');
            return { success: true, message: 'تم رفع المنتجات بنجاح' };
            
        } catch (error) {
            console.error('❌ Error uploading products:', error);
            throw new Error('فشل في رفع المنتجات: ' + error.message);
        }
    }

    async downloadProducts() {
        if (!this.isInitialized) {
            throw new Error('Firebase not initialized');
        }

        try {
            const docRef = doc(db, 'products', 'products_data');
            const docSnap = await getDoc(docRef);
            
            if (docSnap.exists()) {
                const data = docSnap.data();
                const products = JSON.parse(data.products);
                
                console.log('✅ Products downloaded successfully');
                return { success: true, data: products };
            } else {
                console.log('📭 No products data found');
                return { success: false, message: 'لا توجد بيانات منتجات' };
            }
            
        } catch (error) {
            console.error('❌ Error downloading products:', error);
            throw new Error('فشل في تحميل المنتجات: ' + error.message);
        }
    }

    async uploadCustomers(customersData) {
        if (!this.isInitialized) {
            throw new Error('Firebase not initialized');
        }

        try {
            const docRef = doc(db, 'customers', 'customers_data');
            await setDoc(docRef, {
                customers: JSON.stringify(customersData),
                timestamp: Date.now(),
                userId: this.currentUser.uid
            });
            
            console.log('✅ Customers uploaded successfully');
            return { success: true, message: 'تم رفع العملاء بنجاح' };
            
        } catch (error) {
            console.error('❌ Error uploading customers:', error);
            throw new Error('فشل في رفع العملاء: ' + error.message);
        }
    }

    async downloadCustomers() {
        if (!this.isInitialized) {
            throw new Error('Firebase not initialized');
        }

        try {
            const docRef = doc(db, 'customers', 'customers_data');
            const docSnap = await getDoc(docRef);
            
            if (docSnap.exists()) {
                const data = docSnap.data();
                const customers = JSON.parse(data.customers);
                
                console.log('✅ Customers downloaded successfully');
                return { success: true, data: customers };
            } else {
                console.log('📭 No customers data found');
                return { success: false, message: 'لا يوجد عملاء' };
            }
            
        } catch (error) {
            console.error('❌ Error downloading customers:', error);
            throw new Error('فشل في تحميل العملاء: ' + error.message);
        }
    }

    isConnected() {
        return this.isInitialized && this.currentUser !== null;
    }

    getUserId() {
        return this.currentUser ? this.currentUser.uid : null;
    }
}

// Global instance
let webFirebaseManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure other scripts are loaded
    setTimeout(() => {
        webFirebaseManager = new WebFirebaseManager();
    }, 1000);
});

// Export for global use
window.webFirebaseManager = webFirebaseManager;
