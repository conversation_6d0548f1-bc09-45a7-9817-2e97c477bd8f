<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Background gradient -->
    <path android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient
                android:startX="0"
                android:startY="0"
                android:endX="108"
                android:endY="108"
                android:type="linear">
                <item android:offset="0" android:color="#667eea" />
                <item android:offset="0.5" android:color="#764ba2" />
                <item android:offset="1" android:color="#f093fb" />
            </gradient>
        </aapt:attr>
    </path>
    
    <!-- Decorative circles -->
    <circle android:cx="20" android:cy="20" android:r="8" android:fillColor="#ffffff" android:alpha="0.1"/>
    <circle android:cx="88" android:cy="20" android:r="6" android:fillColor="#ffffff" android:alpha="0.1"/>
    <circle android:cx="20" android:cy="88" android:r="6" android:fillColor="#ffffff" android:alpha="0.1"/>
    <circle android:cx="88" android:cy="88" android:r="8" android:fillColor="#ffffff" android:alpha="0.1"/>
    
</vector>
