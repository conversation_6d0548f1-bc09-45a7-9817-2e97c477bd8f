@echo off
echo ========================================
echo     فحص متطلبات بناء التطبيق
echo ========================================
echo.

echo 📂 المسار الحالي:
echo %CD%
echo.

echo 🔍 فحص الملفات المطلوبة...
echo.

if exist "gradlew.bat" (
    echo ✅ gradlew.bat موجود
) else (
    echo ❌ gradlew.bat غير موجود
)

if exist "build.gradle" (
    echo ✅ build.gradle موجود
) else (
    echo ❌ build.gradle غير موجود
)

if exist "app\build.gradle" (
    echo ✅ app\build.gradle موجود
) else (
    echo ❌ app\build.gradle غير موجود
)

if exist "app\src\main\AndroidManifest.xml" (
    echo ✅ AndroidManifest.xml موجود
) else (
    echo ❌ AndroidManifest.xml غير موجود
)

echo.
echo 🔍 فحص Java...
java -version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Java مثبت
) else (
    echo ❌ Java غير مثبت أو غير موجود في PATH
)

echo.
echo 🔍 فحص Android SDK...
if defined ANDROID_HOME (
    echo ✅ ANDROID_HOME: %ANDROID_HOME%
) else (
    echo ❌ ANDROID_HOME غير محدد
)

echo.
echo 🔍 فحص مجلد gradle...
if exist "gradle\wrapper\gradle-wrapper.properties" (
    echo ✅ Gradle Wrapper موجود
) else (
    echo ❌ Gradle Wrapper غير موجود
)

echo.
echo ========================================
echo           ملخص الفحص
echo ========================================

echo.
echo إذا كانت جميع العناصر ✅، يمكنك بناء التطبيق
echo إذا كان هناك ❌، يجب إصلاحها أولاً

echo.
pause
