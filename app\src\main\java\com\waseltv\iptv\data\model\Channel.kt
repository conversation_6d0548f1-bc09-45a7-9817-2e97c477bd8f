package com.waseltv.iptv.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Data class representing an IPTV channel
 */
@Parcelize
data class Channel(
    val id: String,
    val name: String,
    val url: String,
    val logo: String? = null,
    val group: String? = null,
    val language: String? = null,
    val country: String? = null,
    val isLive: Boolean = true,
    val description: String? = null
) : Parcelable

/**
 * Data class representing a channel group/category
 */
@Parcelize
data class ChannelGroup(
    val id: String,
    val name: String,
    val icon: String,
    val color: String,
    val channels: List<Channel>
) : Parcelable

/**
 * Enum for channel categories with icons and colors
 */
enum class ChannelCategory(
    val id: String,
    val displayName: String,
    val icon: String,
    val color: String,
    val description: String
) {
    NEWS("news", "أخبار", "📰", "#FF5722", "قنوات الأخبار والتقارير الإخبارية"),
    SPORTS("sports", "رياضة", "⚽", "#4CAF50", "القنوات الرياضية والمباريات المباشرة"),
    KIDS("kids", "أطفال", "🧸", "#FF9800", "قنوات الأطفال والكرتون"),
    ENTERTAINMENT("entertainment", "ترفيه", "🎬", "#9C27B0", "قنوات الترفيه والأفلام والمسلسلات"),
    GENERAL("general", "عام", "📺", "#2196F3", "القنوات العامة والمتنوعة");

    companion object {
        fun fromString(category: String?): ChannelCategory {
            return when (category?.lowercase()) {
                "أخبار", "news", "إخبارية" -> NEWS
                "رياضة", "sports", "رياضية" -> SPORTS
                "أطفال", "kids", "children", "كرتون" -> KIDS
                "ترفيه", "entertainment", "أفلام", "مسلسلات", "movies" -> ENTERTAINMENT
                else -> GENERAL
            }
        }
    }
}

/**
 * Data class for server configuration
 */
@Parcelize
data class ServerConfig(
    val serverUrl: String,
    val username: String? = null,
    val password: String? = null,
    val playlistUrl: String? = null
) : Parcelable

/**
 * Sealed class for different types of playlist formats
 */
sealed class PlaylistType {
    object M3U : PlaylistType()
    object M3U8 : PlaylistType()
    object JSON : PlaylistType()
    object XML : PlaylistType()
}

/**
 * Data class for playlist information
 */
data class Playlist(
    val url: String,
    val type: PlaylistType,
    val channels: List<Channel> = emptyList(),
    val lastUpdated: Long = System.currentTimeMillis()
)
