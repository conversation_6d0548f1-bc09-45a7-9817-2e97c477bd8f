// Auto Sync System for Diamond Eagles Inventory
// نظام المزامنة التلقائية لتطبيق النسور الماسية

console.log('🔄 تحميل نظام المزامنة التلقائية...');

// Auto Sync Configuration
const AUTO_SYNC_CONFIG = {
    enabled: true,
    interval: 30000, // 30 seconds
    onDataChange: true, // Sync when data changes
    showNotifications: true,
    maxRetries: 3
};

let autoSyncInterval = null;
let lastSyncHash = '';
let syncInProgress = false;

/**
 * تهيئة نظام المزامنة التلقائية
 * Initialize Auto Sync System
 */
function initializeAutoSync() {
    console.log('🔄 تهيئة نظام المزامنة التلقائية...');
    
    // Check if Google Drive is available
    if (typeof window.webFirebaseManager === 'undefined' && typeof gapi === 'undefined') {
        console.log('⚠️ Google Drive غير متاح - المزامنة التلقائية معطلة');
        return;
    }
    
    // Start auto sync interval
    if (AUTO_SYNC_CONFIG.enabled) {
        startAutoSyncInterval();
    }
    
    // Listen for data changes
    if (AUTO_SYNC_CONFIG.onDataChange) {
        setupDataChangeListeners();
    }
    
    // Initial sync check
    setTimeout(() => {
        checkForUpdates();
    }, 5000);
    
    console.log('✅ تم تهيئة نظام المزامنة التلقائية');
}

/**
 * بدء المزامنة التلقائية المجدولة
 * Start Auto Sync Interval
 */
function startAutoSyncInterval() {
    if (autoSyncInterval) {
        clearInterval(autoSyncInterval);
    }
    
    autoSyncInterval = setInterval(() => {
        if (!syncInProgress) {
            checkForUpdates();
        }
    }, AUTO_SYNC_CONFIG.interval);
    
    console.log(`🔄 بدء المزامنة التلقائية كل ${AUTO_SYNC_CONFIG.interval / 1000} ثانية`);
}

/**
 * إيقاف المزامنة التلقائية
 * Stop Auto Sync
 */
function stopAutoSync() {
    if (autoSyncInterval) {
        clearInterval(autoSyncInterval);
        autoSyncInterval = null;
    }
    console.log('⏹️ تم إيقاف المزامنة التلقائية');
}

/**
 * فحص التحديثات من السحابة
 * Check for updates from cloud
 */
async function checkForUpdates() {
    if (syncInProgress) return;
    
    try {
        syncInProgress = true;
        
        // Check if user is connected to Google Drive
        if (!isGoogleDriveConnected()) {
            return;
        }
        
        console.log('🔍 فحص التحديثات من السحابة...');
        
        // Get current data hash
        const currentHash = generateDataHash();
        
        // Check if data changed
        if (currentHash !== lastSyncHash) {
            console.log('📤 تم اكتشاف تغييرات - رفع البيانات...');
            await uploadDataToCloud();
            lastSyncHash = currentHash;
            
            if (AUTO_SYNC_CONFIG.showNotifications) {
                showSyncNotification('تم رفع البيانات للسحابة', 'success');
            }
        }
        
        // Check for remote updates
        await downloadUpdatesFromCloud();
        
    } catch (error) {
        console.error('❌ خطأ في فحص التحديثات:', error);
    } finally {
        syncInProgress = false;
    }
}

/**
 * رفع البيانات للسحابة
 * Upload data to cloud
 */
async function uploadDataToCloud() {
    try {
        // Upload products
        if (typeof uploadProductsToDrive === 'function') {
            await uploadProductsToDrive();
        }
        
        // Upload customers
        if (typeof uploadCustomersToDrive === 'function') {
            await uploadCustomersToDrive();
        }
        
        console.log('✅ تم رفع البيانات بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في رفع البيانات:', error);
        throw error;
    }
}

/**
 * تحميل التحديثات من السحابة
 * Download updates from cloud
 */
async function downloadUpdatesFromCloud() {
    try {
        let hasUpdates = false;
        
        // Check for product updates
        if (typeof downloadProductsFromDrive === 'function') {
            const productsBefore = products.length;
            await downloadProductsFromDrive();
            if (products.length !== productsBefore) {
                hasUpdates = true;
            }
        }
        
        // Check for customer updates
        if (typeof downloadCustomersFromDrive === 'function') {
            const customersBefore = customers.length;
            await downloadCustomersFromDrive();
            if (customers.length !== customersBefore) {
                hasUpdates = true;
            }
        }
        
        if (hasUpdates) {
            console.log('📥 تم تحميل تحديثات جديدة');
            
            // Update UI
            if (typeof displayProducts === 'function') displayProducts();
            if (typeof displayCustomers === 'function') displayCustomers();
            if (typeof updateDashboard === 'function') updateDashboard();
            
            if (AUTO_SYNC_CONFIG.showNotifications) {
                showSyncNotification('تم تحميل تحديثات جديدة', 'info');
            }
            
            // Update hash
            lastSyncHash = generateDataHash();
        }
        
    } catch (error) {
        console.error('❌ خطأ في تحميل التحديثات:', error);
    }
}

/**
 * إنشاء hash للبيانات الحالية
 * Generate hash for current data
 */
function generateDataHash() {
    const data = {
        products: products || [],
        customers: customers || []
    };
    
    const dataString = JSON.stringify(data);
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
        const char = dataString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString();
}

/**
 * إعداد مستمعات تغيير البيانات
 * Setup data change listeners
 */
function setupDataChangeListeners() {
    // Override addProduct function
    const originalAddProduct = window.addProduct;
    if (originalAddProduct) {
        window.addProduct = function(...args) {
            const result = originalAddProduct.apply(this, args);
            scheduleSync();
            return result;
        };
    }
    
    // Override addCustomer function
    const originalAddCustomer = window.addCustomer;
    if (originalAddCustomer) {
        window.addCustomer = function(...args) {
            const result = originalAddCustomer.apply(this, args);
            scheduleSync();
            return result;
        };
    }
    
    console.log('👂 تم إعداد مستمعات تغيير البيانات');
}

/**
 * جدولة المزامنة
 * Schedule sync
 */
function scheduleSync() {
    setTimeout(() => {
        if (!syncInProgress) {
            checkForUpdates();
        }
    }, 2000); // Wait 2 seconds after data change
}

/**
 * فحص اتصال Google Drive
 * Check Google Drive connection
 */
function isGoogleDriveConnected() {
    if (typeof gapi !== 'undefined' && gapi.auth2) {
        const authInstance = gapi.auth2.getAuthInstance();
        return authInstance && authInstance.isSignedIn.get();
    }
    return false;
}

/**
 * عرض إشعار المزامنة
 * Show sync notification
 */
function showSyncNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `sync-notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-sync-alt"></i>
        <span>${message}</span>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * تبديل المزامنة التلقائية
 * Toggle auto sync
 */
function toggleAutoSync() {
    AUTO_SYNC_CONFIG.enabled = !AUTO_SYNC_CONFIG.enabled;
    
    if (AUTO_SYNC_CONFIG.enabled) {
        startAutoSyncInterval();
        showSyncNotification('تم تفعيل المزامنة التلقائية', 'success');
    } else {
        stopAutoSync();
        showSyncNotification('تم إيقاف المزامنة التلقائية', 'warning');
    }
    
    // Save setting
    localStorage.setItem('autoSyncEnabled', AUTO_SYNC_CONFIG.enabled);
}

/**
 * مزامنة يدوية فورية
 * Manual immediate sync
 */
async function performManualSync() {
    if (syncInProgress) {
        showSyncNotification('المزامنة قيد التقدم بالفعل', 'warning');
        return;
    }
    
    showSyncNotification('بدء المزامنة اليدوية...', 'info');
    
    try {
        await checkForUpdates();
        showSyncNotification('تمت المزامنة بنجاح', 'success');
    } catch (error) {
        showSyncNotification('فشل في المزامنة', 'error');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Load saved settings
    const savedAutoSync = localStorage.getItem('autoSyncEnabled');
    if (savedAutoSync !== null) {
        AUTO_SYNC_CONFIG.enabled = savedAutoSync === 'true';
    }
    
    // Initialize after other systems are ready
    setTimeout(() => {
        initializeAutoSync();
    }, 3000);
});

// Export functions for global use
window.toggleAutoSync = toggleAutoSync;
window.performManualSync = performManualSync;
window.stopAutoSync = stopAutoSync;

console.log('✅ تم تحميل نظام المزامنة التلقائية بنجاح');
