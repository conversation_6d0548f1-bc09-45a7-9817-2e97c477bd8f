package com.waseltv.iptv.ui.settings

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.waseltv.iptv.data.model.ServerConfig
import com.waseltv.iptv.data.preferences.PreferencesManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class SettingsUiState(
    val serverUrl: String = "http://maventv.one:80",
    val username: String = "odaitv",
    val password: String = "Odai2030",
    val apiType: String = "Xtream Codes",
    val isLoading: Boolean = false,
    val isSaved: Boolean = false,
    val isConnected: Boolean = true,
    val error: String? = null,
    val userInfo: String? = null
)

class SettingsViewModel(application: Application) : AndroidViewModel(application) {
    private val preferencesManager = PreferencesManager()
    
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    init {
        // السيرفر ثابت، لا حاجة لتحميل إعدادات
        _uiState.value = SettingsUiState()
    }
    
    fun updateServerUrl(serverUrl: String) {
        _uiState.value = _uiState.value.copy(serverUrl = serverUrl)
    }
    
    fun updateUsername(username: String) {
        _uiState.value = _uiState.value.copy(username = username)
    }
    
    fun updatePassword(password: String) {
        _uiState.value = _uiState.value.copy(password = password)
    }
    
    fun updatePlaylistUrl(playlistUrl: String) {
        _uiState.value = _uiState.value.copy(playlistUrl = playlistUrl)
    }
    
    fun saveSettings() {
        // لا حاجة لحفظ إعدادات، السيرفر ثابت
        _uiState.value = _uiState.value.copy(
            isSaved = true,
            error = null
        )
    }

    fun testConnection() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)

            try {
                // TODO: إضافة اختبار الاتصال الفعلي
                kotlinx.coroutines.delay(2000) // محاكاة اختبار الاتصال

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isConnected = true,
                    error = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isConnected = false,
                    error = "فشل في الاتصال بالسيرفر"
                )
            }
        }
    }
    
    fun clearSettings() {
        viewModelScope.launch {
            try {
                preferencesManager.clearServerConfig(getApplication())
                _uiState.value = SettingsUiState()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "حدث خطأ أثناء مسح الإعدادات"
                )
            }
        }
    }
}
